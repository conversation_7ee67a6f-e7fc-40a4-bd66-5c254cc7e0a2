<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('social_posts', function (Blueprint $table) {
            // Change columns to integer type with default value 0
            $table->integer('like')->default(0)->change();
            $table->integer('comment')->default(0)->change();
            $table->integer('view')->default(0)->change();
            $table->integer('share')->default(0)->change();
            $table->integer('friend')->default(0)->change();
            $table->integer('favorite')->default(0)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('social_posts', function (Blueprint $table) {
            // Revert back to string type nullable (original state)
            $table->string('like')->nullable()->change();
            $table->string('comment')->nullable()->change();
            $table->string('view')->nullable()->change();
            $table->string('share')->nullable()->change();
            $table->string('friend')->nullable()->change();
            $table->string('favorite')->nullable()->change();
        });
    }
};
