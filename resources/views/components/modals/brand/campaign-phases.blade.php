{{-- Brand Campaign Phases Modal Component --}}
<div class="modal fade" id="brandCampaignPhasesModal" tabindex="-1" aria-labelledby="brandCampaignPhasesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header border-0 pb-0">
                <h4 class="modal-title fw-bold text-center w-100" id="brandCampaignPhasesModalLabel">The Different Phases</h4>
                <button type="button" class="btn-close position-absolute end-0 me-3" data-bs-dismiss="modal" aria-label="Close" style="font-size: 1.5rem; opacity: 0.8;">X</button>
            </div>
            <div class="modal-body p-4">
                <div class="brand-campaign-phases-container">
                    {{-- Request Phase --}}
                    <div class="phase-card request-phase">
                        <div class="phase-header">
                            <div class="phase-title-container">
                                <span class="phase-chevron d-md-none">^</span>
                                <span class="phase-title">Request phase</span>
                            </div>
                            <div class="phase-duration">
                                <svg class="clock-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                <span class="duration-text">3 Days</span>
                            </div>
                        </div>
                        <div class="phase-content">
                            <div class="phase-section">
                                <strong>During the phase:</strong>
                                <ul>
                                    <li>Influencers have 3 days to respond.</li>
                                    <li>Use "Manage Influencers" (Settings icon) to add or remove requested influencers anytime</li>
                                    <li>Once at least one influencer accepts and all requests are answered, you're free to start the campaign anytime.</li>
                                </ul>
                            </div>
                            <div class="phase-section">
                                <strong>If time runs out:</strong>
                                <ul>
                                    <li>Unanswered requests are auto-declined.</li>
                                    <li>If at least one influencer has accepted, the campaign proceeds to the Payment Phase.</li>
                                    <li>If no influencer accepts, the campaign will be cancelled.</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {{-- Payment Phase --}}
                    <div class="phase-card payment-phase">
                        <div class="phase-header">
                            <div class="phase-title-container">
                                <span class="phase-chevron d-md-none">^</span>
                                <span class="phase-title">Payment phase</span>
                            </div>
                            <div class="phase-duration">
                                <svg class="clock-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                <div class="duration-container">
                                    <span class="duration-text">3 Days</span>
                                    <span class="duration-subtitle">+Remaining Request Time</span>
                                </div>
                            </div>
                        </div>
                        <div class="phase-content">
                            <div class="phase-section">
                                <strong>During the phase:</strong>
                                <ul>
                                    <li>You have 3 days to start the campaign.</li>
                                    <li>No new influencers can be requested, but existing ones can still be removed.</li>
                                </ul>
                            </div>
                            <div class="phase-section">
                                <strong>If time runs out:</strong>
                                <ul>
                                    <li>The campaign is automatically cancelled.</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {{-- Submit Phase --}}
                    <div class="phase-card submit-phase">
                        <div class="phase-header">
                            <div class="phase-title-container">
                                <span class="phase-chevron d-md-none">^</span>
                                <span class="phase-title">Submit phase</span>
                            </div>
                            <div class="phase-duration">
                                <svg class="clock-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                <span class="duration-text">10 Days</span>
                            </div>
                        </div>
                        <div class="phase-content">
                            <div class="phase-section">
                                <strong>During the phase:</strong>
                                <ul>
                                    <li>Each influencer has 10 days to submit their post after campaign start.</li>
                                    <li>You can view submissions as soon as they are confirmed.</li>
                                </ul>
                            </div>
                            <div class="phase-section">
                                <strong>If timer runs out:</strong>
                                <ul>
                                    <li>Influencers who miss the submission deadline will be refunded.</li>
                                    <li>The campaign proceeds to the Review Phase.</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {{-- Review Phase --}}
                    <div class="phase-card review-phase">
                        <div class="phase-header">
                            <div class="phase-title-container">
                                <span class="phase-chevron d-md-none">^</span>
                                <span class="phase-title">Review phase</span>
                            </div>
                            <div class="phase-duration">
                                <svg class="clock-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                <span class="duration-text">7 Days</span>
                            </div>
                        </div>
                        <div class="phase-content">
                            <div class="phase-section">
                                <strong>During the phase:</strong>
                                <ul>
                                    <li>You have 7 days to review and rate the submission.</li>
                                </ul>
                            </div>
                            <div class="phase-section">
                                <strong>If timer runs out:</strong>
                                <ul>
                                    <li>Any influencer submission you do not review will be automatically approved.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Ensure modal is hidden by default */
    #brandCampaignPhasesModal {
        display: none !important;
    }

    #brandCampaignPhasesModal.show {
        display: block !important;
    }

    .brand-campaign-phases-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        padding: 0;
    }

    .brand-campaign-phases-container .phase-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border: 2px solid;
        position: relative;
    }

    .brand-campaign-phases-container .phase-header {
        padding: 16px 20px;
        color: white;
        font-weight: 600;
        font-size: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .brand-campaign-phases-container .phase-title-container {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .brand-campaign-phases-container .phase-chevron {
        font-size: 14px;
        font-weight: bold;
        transform: rotate(180deg);
    }

    .brand-campaign-phases-container .phase-title {
        font-size: 16px;
        font-weight: 600;
    }

    .brand-campaign-phases-container .phase-duration {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
    }

    .brand-campaign-phases-container .clock-icon {
        width: 18px;
        height: 18px;
        stroke: currentColor;
        stroke-width: 2;
    }

    .brand-campaign-phases-container .duration-container {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        line-height: 1.2;
    }

    .brand-campaign-phases-container .duration-text {
        font-size: 14px;
        font-weight: 600;
    }

    .brand-campaign-phases-container .duration-subtitle {
        font-size: 11px;
        font-weight: 400;
        opacity: 0.9;
    }

    .brand-campaign-phases-container .phase-content {
        padding: 20px;
        color: #333;
        font-size: 14px;
        line-height: 1.5;
        background-color: #f8f9fa;
    }

    .brand-campaign-phases-container .phase-section {
        margin-bottom: 16px;
    }

    .brand-campaign-phases-container .phase-section:last-child {
        margin-bottom: 0;
    }

    .brand-campaign-phases-container .phase-section strong {
        display: block;
        margin-bottom: 8px;
        color: #2c3e50;
        font-weight: 600;
    }

    .brand-campaign-phases-container .phase-section ul {
        margin: 0;
        padding-left: 20px;
    }

    .brand-campaign-phases-container .phase-section li {
        margin-bottom: 4px;
        line-height: 1.4;
    }

    /* Phase-specific colors */
    .brand-campaign-phases-container .request-phase {
        border-color: #29B6F6;
    }

    .brand-campaign-phases-container .request-phase .phase-header {
        background: #29B6F6;
    }

    .brand-campaign-phases-container .payment-phase {
        border-color: #FFC107;
    }

    .brand-campaign-phases-container .payment-phase .phase-header {
        background: #FFC107;
        color: #333;
    }

    .brand-campaign-phases-container .submit-phase {
        border-color: #FF7043;
    }

    .brand-campaign-phases-container .submit-phase .phase-header {
        background: #FF7043;
    }

    .brand-campaign-phases-container .review-phase {
        border-color: #66BB6A;
    }

    .brand-campaign-phases-container .review-phase .phase-header {
        background: #66BB6A;
    }

    /* Mobile responsive design */
    @media (max-width: 768px) {
        .brand-campaign-phases-container {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .brand-campaign-phases-container .phase-header {
            padding: 14px 16px;
            font-size: 15px;
        }

        .brand-campaign-phases-container .phase-title {
            font-size: 15px;
        }

        .brand-campaign-phases-container .phase-content {
            padding: 16px;
            font-size: 13px;
        }

        .brand-campaign-phases-container .phase-duration {
            font-size: 13px;
        }

        .brand-campaign-phases-container .duration-text {
            font-size: 13px;
        }

        .brand-campaign-phases-container .duration-subtitle {
            font-size: 10px;
        }

        .brand-campaign-phases-container .clock-icon {
            width: 16px;
            height: 16px;
        }

        .brand-campaign-phases-container .phase-chevron {
            display: block !important;
        }
    }

    /* Desktop hide chevron */
    @media (min-width: 769px) {
        .brand-campaign-phases-container .phase-chevron {
            display: none;
        }
    }
</style>
