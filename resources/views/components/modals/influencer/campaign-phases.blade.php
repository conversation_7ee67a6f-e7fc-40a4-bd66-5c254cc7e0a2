{{-- Campaign Phases Modal Component --}}
<div class="modal fade" id="campaignPhasesModal" tabindex="-1" aria-labelledby="campaignPhasesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header border-0 pb-0">
                <h4 class="modal-title fw-bold text-center w-100" id="campaignPhasesModalLabel">The Different Phases</h4>
                <button type="button" class="btn-close position-absolute end-0 me-3" data-bs-dismiss="modal" aria-label="Close" style="font-size: 1.5rem; opacity: 0.8;">X</button>
            </div>
            <div class="modal-body p-4">
                <div class="campaign-phases-container">
                    {{-- Request Phase --}}
                    <div class="phase-card request-phase">
                        <div class="phase-header">
                            <div class="phase-title-container">
                                <span class="phase-chevron d-md-none">^</span>
                                <span class="phase-title">Request phase</span>
                            </div>
                            <div class="phase-duration">
                                <svg class="clock-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                <span class="duration-text">3 Days</span>
                            </div>
                        </div>
                        <div class="phase-content">
                            <div class="phase-section">
                                <strong>During the phase:</strong>
                                <ul>
                                    <li>You have 3 days to accept the campaign request.</li>
                                    <li>The request is non-binding — the brand can cancel it during Request phase</li>
                                    <li>Once you accept, you'll see the brand's remaining time to start the campaign and Payment Phase starts immediately.</li>
                                </ul>
                            </div>
                            <div class="phase-section">
                                <strong>If timer runs out:</strong>
                                <ul>
                                    <li>If you don't accept in time, the request will be automatically declined.</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {{-- Payment Phase --}}
                    <div class="phase-card payment-phase">
                        <div class="phase-header">
                            <div class="phase-title-container">
                                <span class="phase-chevron d-md-none">^</span>
                                <span class="phase-title">Payment phase</span>
                            </div>
                            <div class="phase-duration">
                                <svg class="clock-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                <div class="duration-container">
                                    <span class="duration-text">3 Days</span>
                                    <span class="duration-subtitle">+ Remaining Request Time</span>
                                </div>
                            </div>
                        </div>
                        <div class="phase-content">
                            <div class="phase-section">
                                <strong>During the phase:</strong>
                                <ul>
                                    <li>After accepting, you'll see how much time the brand has left to start the campaign.</li>
                                    <li>The request is still non-binding — the brand can cancel it during payment phase</li>
                                </ul>
                            </div>
                            <div class="phase-section">
                                <strong>If timer runs out:</strong>
                                <ul>
                                    <li>If the brand doesn't start in time, the campaign is cancelled</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {{-- Submit Phase --}}
                    <div class="phase-card submit-phase">
                        <div class="phase-header">
                            <div class="phase-title-container">
                                <span class="phase-chevron d-md-none">^</span>
                                <span class="phase-title">Submit phase</span>
                            </div>
                            <div class="phase-duration">
                                <svg class="clock-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                <span class="duration-text">10 Days</span>
                            </div>
                        </div>
                        <div class="phase-content">
                            <div class="phase-section">
                                <strong>During the phase:</strong>
                                <ul>
                                    <li>You have 10 days to submit your post after the campaign goes live.</li>
                                    <li>Once you submit, you'll see the brand's remaining time to review the campaign and Review Phase starts immediately.</li>
                                </ul>
                            </div>
                            <div class="phase-section">
                                <strong>If timer runs out:</strong>
                                <ul>
                                    <li>If not submitted on time, your participation is cancelled and refunded.</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {{-- Review Phase --}}
                    <div class="phase-card review-phase">
                        <div class="phase-header">
                            <div class="phase-title-container">
                                <span class="phase-chevron d-md-none">^</span>
                                <span class="phase-title">Review phase</span>
                            </div>
                            <div class="phase-duration">
                                <svg class="clock-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                <div class="duration-container">
                                    <span class="duration-text">7 Days</span>
                                    <span class="duration-subtitle">+ Remaining Submit Time</span>
                                </div>
                            </div>
                        </div>
                        <div class="phase-content">
                            <div class="phase-section">
                                <strong>During the phase:</strong>
                                <ul>
                                    <li>The brand has 7 days to review and rate your submission.</li>
                                </ul>
                            </div>
                            <div class="phase-section">
                                <strong>If timer runs out:</strong>
                                <ul>
                                    <li>If your submission isn't reviewed in time, it will be automatically approved.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Ensure modal is hidden by default */
    #campaignPhasesModal {
        display: none !important;
    }

    #campaignPhasesModal.show {
        display: block !important;
    }

    .campaign-phases-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        padding: 0;
    }

    .phase-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border: 2px solid;
        position: relative;
    }

    .phase-header {
        padding: 16px 20px;
        color: white;
        font-weight: 600;
        font-size: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .phase-title-container {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .phase-chevron {
        font-size: 14px;
        font-weight: bold;
        transform: rotate(180deg);
    }

    .phase-title {
        font-size: 16px;
        font-weight: 600;
    }

    .phase-duration {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
    }

    .clock-icon {
        width: 18px;
        height: 18px;
        stroke: currentColor;
        stroke-width: 2;
    }

    .duration-container {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        line-height: 1.2;
    }

    .duration-text {
        font-size: 14px;
        font-weight: 600;
    }

    .duration-subtitle {
        font-size: 11px;
        font-weight: 400;
        opacity: 0.9;
    }

    .phase-content {
        padding: 20px;
        color: #333;
        font-size: 14px;
        line-height: 1.5;
        background-color: #f8f9fa;
    }

    .phase-section {
        margin-bottom: 16px;
    }

    .phase-section:last-child {
        margin-bottom: 0;
    }

    .phase-section strong {
        display: block;
        margin-bottom: 8px;
        color: #2c3e50;
        font-weight: 600;
    }

    .phase-section ul {
        margin: 0;
        padding-left: 20px;
    }

    .phase-section li {
        margin-bottom: 4px;
        line-height: 1.4;
    }

    /* Phase-specific colors */
    .request-phase {
        border-color: #29B6F6;
    }

    .request-phase .phase-header {
        background: #29B6F6;
    }

    .payment-phase {
        border-color: #FFC107;
    }

    .payment-phase .phase-header {
        background: #FFC107;
        color: #333;
    }

    .submit-phase {
        border-color: #FF7043;
    }

    .submit-phase .phase-header {
        background: #FF7043;
    }

    .review-phase {
        border-color: #66BB6A;
    }

    .review-phase .phase-header {
        background: #66BB6A;
    }

    /* Mobile responsive design */
    @media (max-width: 768px) {
        .campaign-phases-container {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .phase-header {
            padding: 14px 16px;
            font-size: 15px;
        }

        .phase-title {
            font-size: 15px;
        }

        .phase-content {
            padding: 16px;
            font-size: 13px;
        }

        .phase-duration {
            font-size: 13px;
        }

        .duration-text {
            font-size: 13px;
        }

        .duration-subtitle {
            font-size: 10px;
        }

        .clock-icon {
            width: 16px;
            height: 16px;
        }

        .phase-chevron {
            display: block !important;
        }
    }

    /* Desktop hide chevron */
    @media (min-width: 769px) {
        .phase-chevron {
            display: none;
        }
    }
</style>
