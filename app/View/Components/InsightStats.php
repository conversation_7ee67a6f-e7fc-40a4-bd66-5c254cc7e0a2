<?php

namespace App\View\Components;

use App\Models\InfluencerRequest;
use App\Models\InfluencerRequestDetail;
use Illuminate\View\Component;
use App\Helpers\Instagram\InsightsForTypePost;
use App\Helpers\Instagram\InsightsForTypeStory;

class InsightStats extends Component
{
    public InfluencerRequestDetail $influencerRequestDetail;

    // Available in Instagram Story and Post
    public ?int $reach = null;
    public ?int $views = null;
    
    // Available only in Instagram Story
    public ?int $totalInteractions = null;
    public ?int $shares = null;
    
    // Available only in Instagram Post
    public ?int $likes = null;
    public ?int $comments = null;

    public string $reachTitle = 'Number of unique Instagram users that have seen the content at least once.';
    public string $viewsTitle = 'Total number of times the content has been seen.';
    public string $totalInteractionsTitle = 'Total number of likes, saves, comments, and shares.';
    public string $sharesTitle = 'Total number of shares.';
    public string $likesTitle = 'Total number of likes.';
    public string $commentsTitle = 'Total number of comments.';
    
    public function __construct($influencerRequestDetail)
    {
        $this->influencerRequestDetail = $influencerRequestDetail;
        $socialPost = $this->influencerRequestDetail->social_posts;

        if ($socialPost) {
            $formattedInsight = $socialPost->formatInsights($this->influencerRequestDetail->advertising);
            if ($this->influencerRequestDetail->advertising == 'Story') {
                $this->reach = $formattedInsight->reach;
                $this->views = $formattedInsight->views;
                $this->totalInteractions = $formattedInsight->totalInteractions;
                $this->shares = $formattedInsight->shares;
            } else {
                $this->reach = $formattedInsight->reach;
                $this->views = $formattedInsight->views;
                $this->likes = $formattedInsight->likes;
                $this->comments = $formattedInsight->comments;
            }
        }
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('components.insight-stats');
    }
}
