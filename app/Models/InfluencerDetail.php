<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class InfluencerDetail extends Model
{
    protected $fillable = ['user_id', 'category_id', 'influencer_type', 'gender', 'ages', 'content_language', 'content_attracts', 'publish'];

    public function user()
    {
        return $this->hasOne(User::class,'id','user_id');
    }
    
    public function influencerRequestDetail()
    {
        return $this->hasMany(InfluencerRequestDetail::class,'influencer_detail_id','id');
    }

}
