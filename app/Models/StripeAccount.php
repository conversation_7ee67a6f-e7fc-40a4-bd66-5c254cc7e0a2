<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StripeAccount extends Model
{
    protected $fillable = ['stripe_user_id', 'stripe_publishable_key', 'user_id',
        'lock_account', 'balance_issue', 'reason', 'issue_created_at'
        ];

    protected $casts = [
        'lock_account' => 'boolean',
        'balance_issue' => 'boolean',
        'reason' => 'array', // Automatically handles JSON encoding/decoding
        'issue_created_at' => 'datetime', // Casts the timestamp to a Carbon instance
    ];
}
