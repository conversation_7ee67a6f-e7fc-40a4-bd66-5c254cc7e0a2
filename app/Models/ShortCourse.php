<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ShortCourse extends Model
{
    protected $fillable = ['full_name', 'title_user', 'title', 'description', 'user_id', 'subject_area', 'other_service', 'due_date', 'address', 'virtual', 'physical_location', 'cost', 'user_ids', 'user_files','from_time','to_time','admin_per', 'instructor_per', 'status', 'attend_who', 'attend_reason', 'timezone', 'view_count'];

    public function user()
    {
        return $this->hasOne(User::class,'id','user_id');
    }

    public function course_transactions()
    {
        return $this->hasOne(CourseTransaction::class,'short_course_id','id');
    }

    public function instructors()
    {
        return $this->hasMany(CourseInstructor::class);
    }

    public function ratings()
    {
        return $this->hasMany(CourseRating::class);
    }

    public function attendees()
    {
        return $this->hasMany(Attendee::class,'short_courses_id','id');
    }
}
