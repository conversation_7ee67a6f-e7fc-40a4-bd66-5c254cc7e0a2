<?php

namespace App\Models;

use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class InfluencerRequestAccept extends Model
{
    use Notifiable;
    
    protected $fillable = ['user_id', 'influencer_request_detail_id', 'status', 'request_time', 'request_time_accept', 'request'];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'is_time_extension_requested',
        'is_time_extension_approved',
        'is_time_extension_rejected',
        'requested_extension_duration'
    ];

    /**
     * Determine if a time extension has been requested.
     */
    protected function isTimeExtensionRequested(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->request_time !== null && $this->request_time > 0,
        );
    }

    /**
     * Determine if a time extension has been approved.
     */
    protected function isTimeExtensionApproved(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->request_time_accept === '1' || $this->request_time_accept === 1,
        );
    }

    /**
     * Determine if a time extension has been rejected.
     */
    protected function isTimeExtensionRejected(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->request_time_accept === '0' || $this->request_time_accept === 0,
        );
    }

    /**
     * Get the requested extension duration in minutes.
     */
    protected function requestedExtensionDuration(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->request_time ?? 0,
        );
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
 
    public function influencer_request_details()
    {
        return $this->hasOne(InfluencerRequestDetail::class, 'id', 'influencer_request_detail_id');
    }

    public function rating_reviews()
    {
        return $this->hasOne(RatingReview::class, 'influencer_request_accept_id', 'id');
    }
    
    public function complaints()
    {
        return $this->hasOne(Complaint::class, 'influencer_request_accept_id', 'id');
    }
}
