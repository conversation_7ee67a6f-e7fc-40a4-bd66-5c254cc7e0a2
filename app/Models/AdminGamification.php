<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AdminGamification extends Model
{
    protected $fillable = ['select_type', 'type', 'pricing', 'requirement', 'created_at', 'updated_at', 'points_five_star', 'points_four_star', 'points_completed_time', 'quick_response', 'repeat_bookings', 'daily_login', 'successfull_campaign', 'points_one_star', 'points_two_star', 'points_deadlines', 'points_disputes','points_half_time'];
}
