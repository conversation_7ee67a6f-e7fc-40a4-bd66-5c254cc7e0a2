<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContactSupport extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function review()
    {
        return $this->belongsTo(RatingReview::class, 'review_id');
    }

    public function influencerRequestDetail()
    {
        return $this->belongsTo(InfluencerRequestDetail::class);
    }
    public function user()
    {
        return $this->hasOne(User::class,'id','user_id');
    }
}
