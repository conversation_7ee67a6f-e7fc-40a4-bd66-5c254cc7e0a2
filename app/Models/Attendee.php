<?php

namespace App\Models;

use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Model;

class Attendee extends Model
{
	use Notifiable;
    protected $fillable = ['name', 'phone', 'country_code', 'email', 'affiliation', 'address', 'payment_status', 'payment_date', 'txn_id' , 'short_courses_id','receipt','user_id','certificate','attended'];

    public function user()
    {
        return $this->hasOne(User::class,'id','user_id');
    }

    public function course()
    {
        return $this->hasOne(ShortCourse::class,'id','short_courses_id');
    }
}
