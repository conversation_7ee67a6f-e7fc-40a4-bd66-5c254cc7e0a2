<?php

namespace App\Models;

use App\Helpers\Instagram\InsightsForTypePost;
use App\Helpers\Instagram\InsightsForTypeStory;
use Illuminate\Database\Eloquent\Model;

class SocialPost extends Model
{
    protected $fillable = [
        'influencer_request_accept_id',
        'user_id',
        'post_id',
        'media',
        'text',
        'link',
        'type',
        'thumbnail',
        'published_at',
        'insights'
    ];

    protected $attributes = [
        'like' => 0,
        'comment' => 0,
        'view' => 0,
        'share' => 0,
        'friend' => 0,
        'favorite' => 0,
    ];

    protected $casts = [
        'insights' => 'array',
        'published_at' => 'datetime',
        'like' => 'integer',
        'comment' => 'integer',
        'view' => 'integer',
        'share' => 'integer',
        'friend' => 'integer',
        'favorite' => 'integer',
    ];

    public function influencer_request_accepts()
    {
        return $this->hasOne(InfluencerRequestAccept::class,'id', 'influencer_request_accept_id');
    }

    public function formatInsights($advertising = 'Story') {
        $formattedData = [];

        if ($advertising == 'Story') {
            // Use InsightsForTypeStory::METRICS values: ['views', 'reach', 'shares', 'total_interactions']
            foreach (InsightsForTypeStory::METRICS as $metric) {
                if (isset($this->insights[$metric])) {
                    $formattedData[$metric] = $this->insights[$metric];
                } else {
                    $formattedData[$metric] = 0;
                }
            }
            return (object) $formattedData;
        } else {
            // Use InsightsForTypePost::METRICS values: ['likes', 'comments', 'views', 'reach']
            foreach (InsightsForTypePost::METRICS as $metric) {
                if (isset($this->insights[$metric])) {
                    $formattedData[$metric] = $this->insights[$metric];
                } else {
                    $formattedData[$metric] = 0;
                }
            }
            return (object) $formattedData;
        }
    }

    public function social_post_url() {
        return $this->thumbnail;
    }

    public function thumbnail() {
        if (stripos($this->link, 'http') === 0) {
            return $this->link;
        }

        return asset('storage/' . $this->link);
    }

    /**
     * Get the full URL for the social post link
     */
    public function getLinkUrlAttribute()
    {
        if (empty($this->link)) {
            return null;
        }

        // If it's already a full URL, return as is
        if (stripos($this->link, 'http') === 0) {
            return $this->link;
        }

        // Generate the proper storage URL
        return asset('storage/' . $this->link);
    }

    public static function createFakeInstagramPost($influencerId)
    {
        return;
        $data = [
            [
                // "influencer_request_accept_id" => InfluencerRequestAccept::where('user_id', $influencerId)->latest('id')->value('id'),
                "influencer_request_accept_id" => "story",
                "user_id" => $influencerId,
                "post_id" => "18042587006415999",
                "media" => "instagram",
                "text" => "Fake instagram reel",
                "link" => "social_pics/18042587006415999_instagram.mp4",
                "type" => "video",
                "thumbnail" => "https://www.instagram.com/reel/DHuPqOVoEaz/",
                "created_at" => date('Y-m-d H:i:s', strtotime('-3 hours')),
                "updated_at" => date('Y-m-d H:i:s', strtotime('-3 hours')),
                "published_at" => date('Y-m-d H:i:s', strtotime('-2 hours')),
                "like" => random_int(5, 5000),
                "comment" => random_int(5, 5000),
                "view" => random_int(5, 5000),
                "share" => random_int(5, 5000),
                "friend" => null,
                "favorite" => null
            ],
            [
                "influencer_request_accept_id" => "story",
                "user_id" => $influencerId,
                "post_id" => "18142224715389498",
                "media" => "instagram",
                "text" => "Fake instagram video",
                "link" => "social_pics/18142224715389498_instagram.mp4",
                "type" => "video",
                "thumbnail" => "https://instagram.com/stories/motktest/3625275827780017182",
                "created_at" => date('Y-m-d H:i:s', strtotime('-4 hours')),
                "updated_at" => date('Y-m-d H:i:s', strtotime('-4 hours')),
                "published_at" => date('Y-m-d H:i:s', strtotime('-2 hours')),
                "like" => random_int(5, 5000),
                "comment" => random_int(5, 5000),
                "view" => random_int(5, 5000),
                "share" => random_int(5, 5000),
                "friend" => null,
                "favorite" => null
            ]
        ];

        foreach ($data as $item) {
            self::create($item);
        }
    }
}
