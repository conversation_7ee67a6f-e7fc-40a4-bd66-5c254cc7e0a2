<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Complaint extends Model
{
    protected $fillable = ['user_id','influencer_request_accept_id','status', 'comment', 'file'];


    public function influencer_request_accepts()
    {
        return $this->hasOne(InfluencerRequestAccept::class,'id','influencer_request_accept_id');
    }
    
    public function user()
    {
        return $this->hasOne(User::class,'id','user_id');
    }
}
