<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewSendnoInfluencerHasAcceptedYourRequest extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
   public $customer;
    public $row;

    public function __construct($customer, $row)
    {
        $this->customer = $customer;
        $this->row = $row;
    }


    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: env('APP_NAME').' - No influencer has accepted your request',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        // return new Content(
        //     view: 'emails.mail-template',
        // );


        return new Content(
            view: 'emails.noInfluencerHasAcceptedYourRequest-mail-template',
            with: [
                'user' => $this->customer,
                'row' => $this->row,
            ]
        );

        
       // return $this->view('emails.mail-template')
       //              ->with([
       //                  'data' => $this->customer 
       //              ]); 
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
