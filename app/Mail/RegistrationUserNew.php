<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\App;

class RegistrationUserNew extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public $user;
    public function __construct($user)
    {
        $this->user = $user;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        if($this->user->lang == 'De' || App::getLocale()=="de"){
             return new Envelope(
                subject: 'Willkommen bei '.env('APP_NAME').' - Bitte bestätige Dein Account!',
            );
        }else{
            return new Envelope(
                subject: 'Welcome to '.env('APP_NAME').' - Please confirm your account!',
            ); 
        }
       
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        // return new Content(
        //     view: 'emails.mail-template',
        // );

        if($this->user->lang == 'De' || App::getLocale()=="de"){
            return new Content(
                view: 'emails.mail-template-de',
                with: [
                    'data' => $this->user
                ]
            );
        }else{
            return new Content(
                view: 'emails.mail-template',
                with: [
                    'data' => $this->user
                ]
            );
        } 
        
       // return $this->view('emails.mail-template')
       //              ->with([
       //                  'data' => $this->user 
       //              ]); 
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
