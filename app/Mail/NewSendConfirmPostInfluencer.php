<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewSendConfirmPostInfluencer extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
 
    public $customer;
    public $influencerDetail;
    public $influencer;

    public function __construct($customer,$influencerDetail,$influencer)
    {
        $this->customer = $customer;
        $this->influencerDetail = $influencerDetail;
        $this->influencer = $influencer;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: env('APP_NAME').' - Influencer has submitted post',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        // return new Content(
        //     view: 'emails.mail-template',
        // );


        return new Content(
            view: 'emails.ConfirmPostInfluencer-mail-template',
            with: [
                'customer' => $this->customer,
                'influencerDetail' => $this->influencerDetail,
                'influencer' => $this->influencer,
            ]
        );

        
       // return $this->view('emails.mail-template')
       //              ->with([
       //                  'data' => $this->influencer 
       //              ]); 
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
