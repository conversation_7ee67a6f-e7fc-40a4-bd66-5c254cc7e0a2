<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewSendPaymentRefund extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public $user;
    public $detail;
    public $influencer;

    public function __construct($user,$detail, $influencer)
    {
        $this->user = $user;
        $this->detail = $detail;
        $this->influencer = $influencer;
    }


    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: env('APP_NAME').' - Payment refunded due to time expired',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        // return new Content(
        //     view: 'emails.mail-template',
        // );


        return new Content(
            view: 'emails.PaymentRefund-mail-template',
            with: [
                'user' => $this->user,
                'detail' => $this->detail,
                'influencer' => $this->influencer,
            ]
        );


       // return $this->view('emails.mail-template')
       //              ->with([
       //                  'data' => $this->influencer
       //              ]);
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
