<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewSendsocialAlreadyUsed extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
   public $influencer;
    public $sender_influencer;

    public function __construct($influencer,$sender_influencer)
    {
        $this->influencer = $influencer;
        $this->sender_influencer = $sender_influencer;
    }


    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: env('APP_NAME').' - Social Connect',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        // return new Content(
        //     view: 'emails.mail-template',
        // );


        return new Content(
            view: 'emails.socialAlreadyUsed-mail-template',
            with: [
                'influencer' => $this->influencer,
                'sender_influencer' => $this->sender_influencer,
            ]
        );

        
       // return $this->view('emails.mail-template')
       //              ->with([
       //                  'data' => $this->influencer 
       //              ]); 
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
