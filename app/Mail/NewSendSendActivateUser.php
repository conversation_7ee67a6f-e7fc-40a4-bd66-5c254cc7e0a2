<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\App;

class NewSendSendActivateUser extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public $user;
    public $token;

    public function __construct($user,$token)
    {
        $this->user = $user;
        $this->token = $token;
    }
    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        if($this->user->lang == 'De' || App::getLocale()=="de"){
            return new Envelope(
                subject: env('APP_NAME').' - Aktivierungslink',
            );
        }else{
            return new Envelope(
                subject: env('APP_NAME').' - Activation link',
            );
        }
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        // return new Content(
        //     view: 'emails.mail-template',
        // );


        if($this->user->lang == 'De' || App::getLocale()=="de"){

            return new Content(
                view: 'emails.activationLink_de-mail-template',
                with: [
                    'user' => $this->user,
                    'token' => $this->token,
                ]
            );
        }else{

            return new Content(
                view: 'emails.SendActivateUser-mail-template',
                with: [
                    'user' => $this->user,
                    'token' => $this->token,
                ]
            );
        }
 
        
       // return $this->view('emails.mail-template')
       //              ->with([
       //                  'data' => $this->influencer 
       //              ]); 
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
