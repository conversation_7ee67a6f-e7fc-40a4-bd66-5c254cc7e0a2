<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Attachment;

class NewSendSupportContactUser extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public $user;
    public $influencer;
    public $support;

    public function __construct($influencer,$user,$support)
    {
        $this->user = $user;
        $this->influencer = $influencer;
        $this->support = $support;
    }



    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: env('APP_NAME').' - Influencer Contacted',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    { 
        return new Content(
            view: 'emails.SupportContactUser-mail-template',
            with: [
                'user' => $this->user,
                'influencer' => $this->influencer,
                'support' => $this->support,
            ]
        ); 
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments(): array
    {
        if(isset($this->support->file)){
             return [
                Attachment::fromStorage($this->support->file),
            ];
        }else{
             return [];
        } 
    }
}
