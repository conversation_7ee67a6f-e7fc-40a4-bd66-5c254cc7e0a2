<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewSubmitPhaseEnded extends Mailable
{
    use Queueable, SerializesModels;

    
    public $influencer;
    public $row;

    public function __construct($influencer, $row)
    {
        $this->influencer = $influencer;
        $this->row = $row;
    }

    public function envelope()
    {
        return new Envelope(
            subject: env('APP_NAME').' - Submit phase has been ended.',
        );
    }

  
    public function content()
    {
        return new Content(
            view: 'emails.submitPhase-ended-mail-template',
            with: [
                'influencer' => $this->influencer,
                'row' => $this->row,
            ]
        );
    }

    public function attachments()
    {
        return [];
    }
}
