<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewSendRequestAcceptStatusInfluencer extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public $customer;
    public $influencer_request_accept;
    public $influencerDetail;
    public $status;

    public function __construct($customer,$influencer_request_accept,$influencerDetail,$status)
    {
        $this->customer = $customer;
        $this->influencer_request_accept = $influencer_request_accept;
        $this->influencerDetail = $influencerDetail;
        $this->status = $status;
    }



    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: env('APP_NAME').' - The first influencer has accepted your request',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        // return new Content(
        //     view: 'emails.mail-template',
        // );


        return new Content(
            view: 'emails.RequestAcceptStatusInfluencer-mail-template',
            with: [
                'customer' => $this->customer,
                'influencer_request_accept' => $this->influencer_request_accept,
                'influencerDetail' => $this->influencerDetail,
                'status' => $this->status,
            ]
        );

        
       // return $this->view('emails.mail-template')
       //              ->with([
       //                  'data' => $this->user 
       //              ]); 
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
