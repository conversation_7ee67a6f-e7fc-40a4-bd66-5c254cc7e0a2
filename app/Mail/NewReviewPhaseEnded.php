<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewReviewPhaseEnded extends Mailable
{
    use Queueable, SerializesModels;

    public $customer;
    public $row;
    public function __construct($customer, $row)
    {
        $this->customer = $customer;
        $this->row = $row;
    }


    public function envelope()
    {
        return new Envelope(
            subject: env('APP_NAME').' - Submit phase has been ended.',
        );
    }

    public function content()
    {
        return new Content(
            view: 'emails.reviewPhase-ended-mail-template',
            with: [
                'customer' => $this->customer,
                'row' => $this->row,
            ]
        );
    }

    public function attachments()
    {
        return [];
    }
}
