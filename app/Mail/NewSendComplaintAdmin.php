<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Attachment;

class NewSendComplaintAdmin extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public $admin;
    public $influencer;
    public $user;
    public $complaint;
    public $invoice; 

    public function __construct($admin,$influencer,$user,$invoice, $complaint)
    {
        $this->admin = $admin;
        $this->influencer = $influencer;
        $this->user = $user;
        $this->complaint = $complaint;
        $this->invoice = $invoice; 
    }


    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: env('APP_NAME').' - Complaint registered',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        // return new Content(
        //     view: 'emails.mail-template',
        // );


        return new Content(
            view: 'emails.ComplaintAdmin-mail-template',
            with: [
                'admin' => $this->admin,
                'user' => $this->user,
                'influencer' => $this->influencer,
                'complaint' => $this->complaint,
                'invoice' => $this->invoice,  
            ]
        );

        
       // return $this->view('emails.mail-template')
       //              ->with([
       //                  'data' => $this->influencer 
       //              ]); 
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments(): array
    {
        if(isset($this->complaint->file)){
             return [
                Attachment::fromStorage($this->complaint->file),
            ];
        }else{
             return [];
        }
    }
}
