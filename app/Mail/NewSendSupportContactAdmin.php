<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Attachment;

class NewSendSupportContactAdmin extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
 
    public $user;
    public $influencer;
    public $support;
    public $admin;

    public function __construct($admin,$influencer,$user,$support)
    {
        $this->admin = $admin;
        $this->user = $user;
        $this->influencer = $influencer;
        $this->support = $support;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: env('APP_NAME').' - Influencer Contacted',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        // return new Content(
        //     view: 'emails.mail-template',
        // );


        return new Content(
            view: 'emails.SupportContactAdmin-mail-template',
            with: [
                'admin' => $this->admin,
                'user' => $this->user,
                'influencer' => $this->influencer,
                'support' => $this->support,
            ]
        );

        
       // return $this->view('emails.mail-template')
       //              ->with([
       //                  'data' => $this->influencer 
       //              ]); 
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments(): array
    {
        if(isset($this->support->file)){
             return [
                Attachment::fromStorage($this->support->file),
            ];
        }else{
             return [];
        } 
    }
}
