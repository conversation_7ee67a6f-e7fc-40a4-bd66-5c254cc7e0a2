<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Lab404\Impersonate\Services\ImpersonateManager;

class ImpersonateController extends Controller
{
    /**
     * @var ImpersonateManager
     */
    protected $manager;

    /**
     * ImpersonateController constructor.
     */
    public function __construct()
    {
        $this->middleware('auth:admin');
        $this->manager = app('impersonate');
    }

    /**
     * Start impersonating a user
     *
     * @param Request $request
     * @param int $id
     * @param string|null $guardName
     * @return \Illuminate\Http\RedirectResponse
     */
    public function take(Request $request, $id, $guardName = null)
    {
        // Get the current admin user
        $impersonator = Auth::guard('admin')->user();
        
        // Check if admin can impersonate
        if (!$impersonator || !$impersonator->canImpersonate()) {
            abort(403, 'You are not authorized to impersonate users.');
        }

        // Find the user to impersonate
        $userToImpersonate = User::findOrFail($id);
        
        // Check if user can be impersonated
        if (!$userToImpersonate->canBeImpersonated()) {
            abort(403, 'This user cannot be impersonated.');
        }

        // Store the original admin user info in session
        session([
            'impersonated_by' => $impersonator->id,
            'impersonator_guard' => 'admin',
            'impersonator_guard_using' => 'web'
        ]);

        // Login as the target user on the web guard
        Auth::guard('web')->login($userToImpersonate);
        
        // Logout from admin guard to prevent conflicts
        Auth::guard('admin')->logout();

        // Fire the impersonation event
        event(new \Lab404\Impersonate\Events\TakeImpersonation($impersonator, $userToImpersonate));

        // Determine redirect based on user type
        $redirectTo = $this->getRedirectForUserType($userToImpersonate);

        return redirect()->to($redirectTo);
    }

    /**
     * Stop impersonating and return to admin
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function leave(Request $request)
    {
        // Check if currently impersonating
        if (!session()->has('impersonated_by')) {
            return redirect()->route('admin.dashboard');
        }

        // Get the impersonated user and original admin
        $impersonatedUser = Auth::guard('web')->user();
        $originalAdminId = session('impersonated_by');
        $originalAdmin = User::find($originalAdminId);

        if (!$originalAdmin) {
            // Clear session and redirect if original admin not found
            session()->forget(['impersonated_by', 'impersonator_guard', 'impersonator_guard_using']);
            return redirect()->route('admin.dashboard');
        }

        // Fire the leave impersonation event
        if ($impersonatedUser) {
            event(new \Lab404\Impersonate\Events\LeaveImpersonation($originalAdmin, $impersonatedUser));
        }

        // Logout from web guard
        Auth::guard('web')->logout();

        // Login back as admin
        Auth::guard('admin')->login($originalAdmin);

        // Clear impersonation session data
        session()->forget(['impersonated_by', 'impersonator_guard', 'impersonator_guard_using']);

        // Redirect to admin dashboard
        $redirectTo = config('laravel-impersonate.leave_redirect_to', '/admin');
        
        if ($redirectTo === 'back') {
            return redirect()->back();
        }

        return redirect()->to($redirectTo);
    }

    /**
     * Get the appropriate redirect URL based on user type
     *
     * @param User $user
     * @return string
     */
    protected function getRedirectForUserType($user)
    {
        switch ($user->user_type) {
            case 'influencer':
                // Influencers go to their onboarding/dashboard page
                return '/influencer-onboarding';

            case 'customer':
                // Customers go to their profile page
                return '/my-profile';

            case 'admin':
                // Admins (shouldn't happen, but just in case)
                return '/admin';

            default:
                // Default fallback to home
                return '/home';
        }
    }
}
