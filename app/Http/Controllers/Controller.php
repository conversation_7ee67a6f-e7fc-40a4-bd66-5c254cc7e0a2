<?php

namespace App\Http\Controllers;

use App\Models\SocialConnect;
use App\Models\SocialPost;
use DateTimeZone;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Contracts\Session\Session;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;
    public function testDate()
    {
        $data_row               = SocialPost::where('id',30)->first();
        $msg_time = \DateTime::createFromFormat('Y-m-d H:i:s',$data_row->published_at,new DateTimeZone('UTC'));
        $msg_time_formatted = $msg_time->format('M d,Y H:i'); 
        $now = new \DateTime('now', new \DateTimeZone('UTC'));
        // Subtract 3 days from $now
        $threeDaysAgo = (clone $now)->sub(new \DateInterval('P3D'));
        $interval = $threeDaysAgo->diff($msg_time);
        $seconds = $interval->days * 24 * 60 * 60 +
           $interval->h * 60 * 60 +
           $interval->i * 60 +
           $interval->s;
        dd($seconds);
    }
    public function getInfo()
    {
        $userSocial                      = SocialConnect::whereId(172)->first();   
        $url = "https://graph.facebook.com/v18.0/me/accounts?access_token=".$userSocial->token;
        $client = new Client();
        try{
            $response = $client->request('GET', $url);
            $content = $response->getBody()->getContents();
            $oAuth = json_decode($content);
             dd(@$oAuth);
            $userSocialPages = @$oAuth->data;
            if($userSocial->media=='instagram')
            {
                // try{
                // dd($userSocial);
                    $provider = 'instagram';

                    $url = "https://graph.facebook.com/v18.0/".@$userSocialPages[0]->id."?fields=instagram_business_account&access_token=".@$userSocial->token;
                    $client = new Client();
                    $response = $client->request('GET', $url);
                    $content = $response->getBody()->getContents();
                    $oAuth = json_decode($content);
                    $insta_user_id = @$oAuth->instagram_business_account->id;
                    if($insta_user_id=='')
                    {
                        Log::info("Instagram id not found for social media with Id # ".$insta_user_id);
                    }
                    $url = "https://graph.facebook.com/v18.0/".$insta_user_id."?fields=id,name,username,profile_picture_url,followers_count&access_token=".@$userSocial->token;
                    $client = new Client();
                    $response = $client->request('GET', $url);
                    $content = $response->getBody()->getContents();
                    $oAuth = json_decode($content); 
                    //$userSocial = new \stdClass();
                    $userSocial->token       = $oAuth->access_token;
                    $userSocial->name        = (@$oAuth->username)?@$oAuth->username:@$oAuth->name;
                    $userSocial->followers   = @$oAuth->followers_count;
                    $userSocial->url         = "https://www.instagram.com/".@$oAuth->username;
                    $picture = @$oAuth->profile_picture_url; 
                    if(isset($picture)){ 
                        $fileContents = file_get_contents( $picture );
                        $filename = str_random(40);
                        Storage::disk('public')->put('social_pics/' . $filename . '.jpg', $fileContents);
                    $userSocial->picture    = 'social_pics/' . $filename.'.jpg';
                    }
                    $userSocial->token_secret = $insta_user_id  ;

                    echo "Instagram Posts";

                    $accessToken = @$userSocial->token; 

                  echo  $url = 'https://graph.facebook.com/v18.0/'.$insta_user_id.'/media?fields=id,caption,media_type,thumbnail_url,media_url,permalink,timestamp&access_token='.$accessToken;

                    $ch = curl_init($url);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    $data = json_decode(curl_exec($ch));
                    curl_close($ch);

                    // echo "<pre>";
                    // print_r($data);
                    // echo "</pre>";

                    foreach ($data->data as $key => $value) {

                        // for getting likes, comments, shares, views for different types of posts:
                        // https://developers.facebook.com/docs/instagram-api/reference/ig-media/insights/

                        $url = 'https://graph.facebook.com/v18.0/'.$value->id.'/insights?metric=likes,comments,shares,reach&access_token='.$accessToken;

                        $ch = curl_init($url);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        $data = json_decode(curl_exec($ch));
                        curl_close($ch);

                        // echo "<pre>";
                        // print_r($data);
                        // echo "</pre>"; 
                    }  
                // }
                
                // catch(\Exception $e){
                //     Session::put('influencer_error','Instagram connect did not work. Please give the needed rights. '); 
                //     return "<script type='text/javascript'> self.close(); window.opener.refreshParentError();</script>";
                // }

            }
            elseif($userSocial->media=='facebook')
            { 
                // dd($userSocial);
                //try{
                    $url = "https://graph.facebook.com/v18.0/".@$userSocialPages[0]->id."?fields=id,name,username,picture,followers_count,link,access_token&access_token=".@$userSocial->token;
                    $client = new Client();
                    $response = $client->request('GET', $url);
                    $content = $response->getBody()->getContents();
                    $oAuth = json_decode($content); 
                    $userSocial->token = $oAuth->access_token ;
                    $userSocial->social_id = $userSocial->page_access_token ;
                    //$userSocial = new \stdClass();
                    $userSocial->name = (@$oAuth->username)?@$oAuth->username:@$oAuth->name;
                    $userSocial->followers = @$oAuth->followers_count;
                    $userSocial->url = @$oAuth->link;
                    $picture = @$oAuth->picture->data->url; 
                    if(isset($picture)){ 
                        $fileContents = file_get_contents( $picture );
                        $filename = str_random(40);
                        Storage::disk('public')->put('social_pics/' . $filename . '.jpg', $fileContents);
                    $userSocial->picture    = 'social_pics/' . $filename.'.jpg';
                    }
                    $userSocial->token_secret = @$userSocialPages[0]->id; 
                    $accessToken = $userSocial->token;
                    $userSocial->save();
                    // $fb_user_id = $userSocial->id;

                    // $url = 'https://graph.facebook.com/'.@$userSocialPages[0]->id.'/posts?access_token='.$userSocial->page_access_token;

                    // $ch = curl_init($url);
                    // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    // $data = json_decode(curl_exec($ch));
                    // curl_close($ch);

                    // // echo "<pre>";
                    // // print_r($data);
                    // // echo "</pre>"; 
                    // // exit;

                    // foreach ($data->data as $key => $value) {
                    //     $url = 'https://graph.facebook.com/'.$value->id.'?fields=likes.summary(total_count)&access_token='.$accessToken;

                    //     $ch = curl_init($url);
                    //     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    //     $data = json_decode(curl_exec($ch));
                    //     curl_close($ch);
                    //     $url = 'https://graph.facebook.com/'.$value->id.'/insights?metric=post_impressions,post_reactions_like_total&access_token='.$userSocial->page_access_token;

                    //     $ch = curl_init($url);
                    //     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    //     $data = json_decode(curl_exec($ch));
                    //     curl_close($ch);
                    // }

                    // exit;

                // }
                
                // catch(\Exception $e){
                //     Session::put('influencer_error','Facebook connect did not work. Please give the needed rights. Click here for more info.'); 
                //     return "<script type='text/javascript'> self.close(); window.opener.refreshParentError();</script>";
                // }

            } 
        }catch(Exception $e){
           echo $e->getMessage();
        }
     
    }

   public function getInfo2()
   {

   }
}
