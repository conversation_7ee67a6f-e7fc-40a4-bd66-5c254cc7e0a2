<?php

namespace App\Http\Controllers;

use App\Models\InfluencerRequestDetail;
use App\Models\User;
use Illuminate\Http\Request;
use Tests\Feature\StripePaymentTest;

class TestingController extends Controller
{
    /**
     * Test the Stripe payment process calculations.
     *
     * @param Request $request
     * @param string|null $campaignId
     * @return \Illuminate\Http\Response
     */
    public function testStripePayment(Request $request, $campaignId = null)
    {
        try {
            // Capture output to a buffer
            ob_start();
            
            // Use the debug method from our test class
            $test = new StripePaymentTest();
            $test->debugPaymentCalculations($campaignId);
            
            // Return the debug output
            $output = ob_get_clean();
            return response()->view('test-stripe-payment', [
                'output' => nl2br($output),
                'campaignId' => $campaignId
            ]);
            
        } catch (\Exception $e) {
            $output = ob_get_clean();
            return response()->view('test-stripe-payment', [
                'output' => nl2br($output),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'campaignId' => $campaignId
            ]);
        }
    }
}
