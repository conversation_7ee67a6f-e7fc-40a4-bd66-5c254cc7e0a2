<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\InfluencerRequestDetail;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Stripe\Account;
use Stripe\Stripe;

class PayoutController extends Controller
{
    public function getPaidPayment(Request $request)
    {
        $campaigns = InfluencerRequestDetail::whereHas('influencerdetails', function ($query) {
            $query->where('user_id', Auth::id());
        })->whereHas('invoices', function ($query) {
            $query->where('payment_status', 'paid');
            $query->where('is_refunded', 0);
        })
            ->where('payment_status', InfluencerRequestDetail::STATUS_COMPLETED)
            ->whereNotNull('invoice_id')
            ->whereNull('refund_reason')
            ->whereNotNull('completed_at')
            ->where('is_cashed_out', 0)
            ->whereNull('cashed_out_at')
            ->where('review', 1)
            ->orderBy('completed_at', 'desc');

        if(!$campaigns->exists()){
            return redirect('/cash-out')->with('error', 'Not campaigns found for Cash Out');
        }

        // Determine the next batch number
        $nextBatch = InfluencerRequestDetail::whereHas('influencerdetails', function ($query) {
            $query->where('user_id', Auth::id());
        })
            ->max('payout_batch') ?? 0; // Get the max payout_batch or default to 0

        $nextBatch += 1; // Increment to determine the new batch number

        $pending = InfluencerRequestDetail::whereHas('influencerdetails', function ($query) {
            $query->where('user_id', Auth::id());
        })->whereHas('invoices', function ($query) {
            $query->where('payment_status', 'paid');
            $query->where('is_refunded', 0);
        })
            ->where('payment_status', 'pending')
            ->whereNotNull('invoice_id')
            ->whereNull('refund_reason')
            ->whereNotNull('completed_at')
            ->where('is_cashed_out', 0)
            ->whereNull('cashed_out_at')
            ->where('review', 1)
            ->when(config('app.env') === 'local', function ($query) {
                $query->whereDate('completed_at', '<=', Carbon::now()->subMinutes(15));
            }, function ($query) {
                $query->whereDate('completed_at', '<=', Carbon::now()->subDays(14));
            })
            ->orderBy('completed_at', 'desc')
            ->sum('cash_out_amount');

        $pending = $pending * 100;

        $amount = $campaigns->sum('cash_out_amount');
        // Ensure $amount is a number
        $amount = $amount ?? 0; // Convert null to 0 if necessary

        // Round down to 2 decimal places
        $amount = floor($amount * 100) / 100;

        $amount  = $amount * 100;

        $connectAccountId = Auth::user()->stripeAccount->stripe_user_id;

//        dd($amount, $campaigns);
        try {
            Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));

            // Retrieve account details to check if onboarding is complete
            $account = Account::retrieve($connectAccountId);

            // Check account status
            $this->checkAccountStatus($connectAccountId);

            // Check balance
            $availableBalance = $this->checkBalance($connectAccountId);

            $availableBalance = $availableBalance - $pending;

            if ($availableBalance < $amount) {

                Auth::user()->stripeAccount->update([
                    'balance_issue' => true,
                    'reason' => json_encode(['error' => 'Insufficient funds for cashout']),
                ]);

                return redirect('/cash-out')->with('error', 'Insufficient funds for cashout');
            }

            // Create payout
            $payout = \Stripe\Payout::create([
                'amount' => $amount, // Amount in the smallest currency unit (e.g., cents for USD)
                'currency' => 'eur',
                'method' => 'standard', // 'instant' for instant payouts (requires eligibility)
            ], [
                'stripe_account' => $connectAccountId, // Replace with the connected account ID
            ]);

            if($payout){
                $campaigns->update([
                    'payout_batch' => $nextBatch,
                    'is_cashed_out' => 1,
                    'cashed_out_at' => now(),
                    'payment_status' => InfluencerRequestDetail::STATUS_CASHED_OUT
                ]);

                Auth::user()->stripeAccount->update([
                    'balance_issue' => false,
                    'lock_account' => false,

                ]);

                return redirect('/cash-out')->with('success', 'Cash Out request has been created successfully, Please check stripe for details');
            }else{
                Auth::user()->stripeAccount->update([
                    'lock_account' => true,
                    'reason' => json_encode(['error' => 'Payout request failed']),
                ]);

                return redirect('/cash-out')->with('error', 'Payout request failed');
            }

        } catch (\Exception $e) {

            Auth::user()->stripeAccount->update([
                'lock_account' => true,
                'reason' => $e->getMessage(),
            ]);

            return redirect('/cash-out')->with('error', $e->getMessage());
        }
    }

    private function checkBalance($connectAccountId)
    {
        $balance = \Stripe\Balance::retrieve([
            'stripe_account' => $connectAccountId
        ]);
        return collect($balance->available)->sum('amount');
    }

    private function checkAccountStatus($connectAccountId)
    {
        $account = \Stripe\Account::retrieve($connectAccountId);
        if ($account->requirements->disabled_reason) {
            throw new \Exception('Account is disabled: ' . $account->requirements->disabled_reason);
        }
    }
}
