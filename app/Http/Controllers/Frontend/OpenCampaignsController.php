<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Jobs\NewallInfluencersHaveCheckedYourRequest;
use App\Jobs\NewRequestAcceptStatusInfluencer;
use App\Jobs\NewRequestCancelInfluencer;
use App\Jobs\NewRequestInfluencer;
use App\Jobs\NewStartCampaignInfluencer;
use App\Jobs\NewyourCampaignisMovingForward;
use App\Models\AdminComission;
use App\Models\AdminGamification;
use App\Models\AdvertisingMethodNewPrice;
use App\Models\Campaign;
use App\Models\CampaignRequestTime;
use App\Models\Category;
use App\Models\Hashtag;
use App\Models\InfluencerDetail;
use App\Models\InfluencerRequestAccept;
use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestTime;
use App\Models\Invoice;
use App\Models\RequestTask;
use App\Models\SavedCard;
use App\Models\Statistic;
use App\Models\User;
use App\Models\City;
use App\Models\State;
use App\Notifications\allInfluencersHaveCheckedYourRequest;
use App\Notifications\RequestAcceptStatusInfluencer;
use App\Notifications\RequestCancelInfluencer;
use App\Notifications\RequestInfluencer;
use App\Notifications\StartCampaignInfluencer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\CardException;
use Stripe\PaymentMethod;

class OpenCampaignsController extends Controller
{
    public function buildOpenRequestsPage() {
        $influencerCampaignDetails = InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->leftjoin('campaigns', 'campaigns.campaign_id', '=', 'influencer_request_details.compaign_id')
            ->select('influencer_request_details.*', 'campaigns.has_started', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request', 'influencer_request_accepts.request_time', 'influencer_request_accepts.request_time_accept', 'influencer_request_accepts.id as influencer_request_accept_id')
            ->where('influencer_details.user_id', Auth::id())
            ->where('campaigns.has_started', false)
            ->orderBy('influencer_request_details.id', 'desc');

        $influencerCampaignDetails = $influencerCampaignDetails->get();

        foreach ($influencerCampaignDetails as $influencerCampaignDetail) {
            $requested_time = InfluencerRequestTime::where(
                'influencer_request_accept_id',
                $influencerCampaignDetail->influencer_request_accept_id
            )
            ->latest()
            ->first();

            $influencerCampaignDetail->requested_time = $requested_time;
            $influencerCampaignDetail->tasks  = RequestTask::where('influencer_request_detail_id', $influencerCampaignDetail->compaign_id)->get();
        }

        $campaignRequestTime = CampaignRequestTime::first();

        return view('front-user.pages.influencer.open-requests', compact('influencerCampaignDetails', 'campaignRequestTime'));
    }

    public function buildOpenCampaignsPage() {
        $influencerCampaignDetails =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->leftjoin('campaigns', 'campaigns.campaign_id', '=', 'influencer_request_details.compaign_id')
            ->select('influencer_request_details.*', 'campaigns.has_started', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request', 'influencer_request_accepts.id as influencer_request_accept_id', DB::raw('COUNT(influencer_request_accepts.id) AS total_count'), DB::raw('SUM(influencer_request_accepts.request) AS accept_request'), DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count'))
            ->where('influencer_request_details.user_id', Auth::id())
            ->where('influencer_request_details.status', NULL)
            ->where('campaigns.has_started', false)
            ->groupBy('influencer_request_details.compaign_id')
            ->orderBy('influencer_request_details.id', 'desc');

        $influencerCampaignDetails = $influencerCampaignDetails->get();

        foreach ($influencerCampaignDetails as $influencerCampaignDetail) {
            $influencerDetails =  InfluencerDetail::leftjoin('influencer_request_details', function ($join) use ($influencerCampaignDetail) {
                $join->on('influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')->where('influencer_request_details.compaign_id', $influencerCampaignDetail->compaign_id);
            })
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->leftjoin('social_connects',  'social_connects.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('advertising_method_new_prices',  'advertising_method_new_prices.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('hashtags',  'hashtags.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->select('influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_details.*', 'social_connects.media', 'social_connects.url', 'social_connects.picture', 'social_connects.name as username', 'social_connects.followers', 'advertising_method_new_prices.*', 'hashtags.tags', 'influencer_request_details.advertising', 'influencer_request_details.id as request_status', 'influencer_request_accepts.id as accept_id', 'influencer_request_accepts.request as accept_request')
            ->where('social_connects.media', $influencerCampaignDetail->media)
            ->where('advertising_method_new_prices.type', $influencerCampaignDetail->post_type)
            ->where('advertising_method_new_prices.media', $influencerCampaignDetail->media);

            if (isset($influencerCampaignDetail->media)) {
                $influencerDetails->where('advertising_method_new_prices.media', $influencerCampaignDetail->media);
            }

            if (isset($influencerCampaignDetail->category_id)) {
                $condition = " 1 = 1 ";
                $subject_con_arr = [];
                foreach ($influencerCampaignDetail->category_id as $cat) {
                    if ($cat != '') {
                        $subject_con_arr[] = " FIND_IN_SET('" . $cat . "', influencer_details.category_id) ";
                    }
                }

                if (count($subject_con_arr) > 0) {
                    $condition .= ' and (' . implode(' or ', $subject_con_arr) . ')';
                }

                $influencerDetails->whereRaw($condition);
            }

            $influencerDetails = $influencerDetails->where('influencer_details.publish', 'Publish')->where('users.status', 1);
            $influencerDetails = $influencerDetails->groupBy('influencer_details.id')->get();

            foreach ($influencerDetails as $influencerDetail) {
                $hashtags = Hashtag::where('user_id', $influencerDetail->i_user_id)->get('tags');
                $influencerDetail->tags =  $hashtags;

                $category = Category::where('id', $influencerDetail->category_id)->get('name');
                $influencerDetail->category =   $category->implode('name', ',');
            }

            $influencerCampaignDetail->influencerslist  = $influencerDetails;
            $influencerCampaignDetail->tasks  = RequestTask::where('influencer_request_detail_id', $influencerCampaignDetail->compaign_id)->get();

            $influencerCampaignDetail->campaign_details = Campaign::where('campaign_id', $influencerCampaignDetail->compaign_id)->first();
        }

        $AdminComission = AdminComission::first();
        $campaignRequestTime = CampaignRequestTime::first();

        return view('front-user.pages.brand.open-campaigns', compact('influencerCampaignDetails', 'AdminComission', 'campaignRequestTime'));
    }

    /**
     * This method is executed when the brand user process to start the campaign.
     * 
     * DEPRECATED
     * 
     * The new version is in StripeMarketplaceController::handleSuccessfulPayment()
     * 
     */
    public function requestStartCampaign(Request $request)
    {
        // formData has the following fields when entering a new card:
        // {name: '_token', value: "6MKvFmXbXxBiY4sFUCu0SMsjg5Ek8LnFrYzc4yzh'}
        // {name: 'compaign_id', value: 'C-000031'}
        // {name: 'name_on_card', value: 'John Doe'}
        // {name: 'amount', value: '8.33 '}
        // {name: 'accept_platform_fees_nonrefundable', value: '1'}
        // {name: 'accept_successful_condition', value: '1')
        // {name: 'payment_method_id', value: 'pm_1R0EX2Fv5DX420hyxpB43Y97' )
        // {name: 'card_brand', value: "visa"}
        // {name: 'last4', value: '4242'}
        // {name: 'save_card', value: '1'}
        // {name: 'expiry_month', value: '12'}
        // {name: 'expiry_year', value: '26'}
        $formData = request()->except(['_token']);
        $formData['user_id'] = Auth::id();
        $formData['type'] = ''; // card brand, eg, visa, mastercard, etc

        \Stripe\Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));

        $stripePaymentMethodId = null;

        if (!empty($formData['payment_method_id'])) {
            $stripePaymentMethodId = $formData['payment_method'] = $formData['payment_method_id'];

            // try {
                
            // } catch (CardException $exception) {
            //     return redirect('/open-campaigns')->with('error', $exception->getMessage());
            // } catch (\Exception $exception) {
            //     return redirect('/open-campaigns')->with('error', $exception->getMessage());
            // }

            if (isset($formData['save_card']) && $formData['save_card'] == 1) {
                $cardDetails = $formData;
                $cardDetails['cvv'] = '';
                $cardDetails['card_number'] = $formData['last4'];
                $cardDetails['type'] = $formData['card_brand'];
                $card = SavedCard::create($cardDetails);
                // TODO add validation that $card creation was successful
            }
        } else {
            $card = SavedCard::whereUserId(Auth::id())
                ->orderBy('id', 'desc')
                ->first();
            // TODO add validation that $card retrieval was successful
            $stripePaymentMethodId = $card->payment_method;
        }

        if ($stripePaymentMethodId == null || empty($stripePaymentMethodId)) {
            return redirect('/open-campaigns')->with(
                'error',
                'Something went wrong. Please try again later. If the problem persists, please contact the support.'
            );
        }

        // TODO only for testing, remove it before committing code
        if (config('app.env') === 'localhost') {
            return redirect('/open-campaigns')->with('error', 'Localhost, payment is disabled.');
        }

        // This is a brand user
        $userBrand = User::find(Auth::id());

        // If the brand user had not stripe_id, then create a stripe customer
        // for him and assign the stripe id
        if (is_null($userBrand->stripe_id)) {
            $userBrandStripeCustomer = $userBrand->createAsStripeCustomer();
            $userBrand->update(['stripe_id' => $userBrandStripeCustomer->stripe_id]);
        }

        try {
            $userBrand->createOrGetStripeCustomer();
            $userBrand->addPaymentMethod($stripePaymentMethodId);
        } catch (CardException $exception) {
            return redirect('/open-campaigns')->with('error', $exception->getMessage());
        } catch (\Exception $exception) {
            return redirect('/open-campaigns')->with('error', $exception->getMessage());
        }

        // make a condition to filter out rejected influncers
        $influencers = InfluencerRequestDetail::where('compaign_id', $formData['compaign_id'])->get();

        // Calculate the total price and platform fee
        $totalAmount = $influencers->sum('discount_price');
        $platformFee = max($totalAmount * 0.05, 2);

        // Calculate 19% VAT on the platform fee
        $platformVatAmount = $platformFee * 0.19;

        $platformTotal = $platformFee + $platformVatAmount;
        
        // Get customer's default payment method
        $customer = \Stripe\Customer::retrieve($userBrand->stripe_id);

        // First update the brand's billing address
        // TODO perhaps does not needed to update it every time, oder?
        // Better to add it in the user update settings, when they
        // change their address
        
        // TODO Note the difference between \Stripe\Customer::update() vs \Strip\Account::update().
        // What is their difference. Will those both work for Influencer (service provider) account?

        $cityName = '';
        $stateName = '';
        if (!empty($userBrand->city) && is_numeric($userBrand->city)) {
            $city = City::find($id = $userBrand->city);
            if (!empty($city->name)) {
                $cityName = $city->name;
            }

            if (!empty($city->state_id) && is_numeric($city->state_id)) {
                $state = State::find($city->state_id);
                if (!empty($state->state)) {
                    $stateName = $state->state;
                }
            }
        }

        $customerData = [
            'address' => [
                'line1' => $userBrand->street,
                // 'city' => $userBrand->city, // it's city id in userBrand
                // 'state' => $userBrand->state, // it's state id or empty in userBrand
                'postal_code' => $userBrand->zip_code,
                'country' => 'DE', // $userBrand->country, // TODO This is numerical code in the database, need to find the two-letter iso code for country
            ]
        ];

        if ($cityName != '') {
            $customerData['address']['city'] = $cityName;
        }

        if ($stateName != '') {
            $customerData['address']['state'] = $stateName;
        }

        if (!empty($userBrand->company_name)) {
            $customerData['name'] = $userBrand->company_name;
        }

        $updatedUserBrandCustomerObject = \Stripe\Customer::update(
            $userBrand->stripe_id,
            $customerData
        );

        // First create platform fee invoice
        $platformFeeInvoice = \Stripe\Invoice::create([
            'customer' => $userBrand->stripe_id,
            'auto_advance' => false,
            'collection_method' => 'charge_automatically',
            'metadata' => [
                'type' => 'platform_fee',
                'refundable' => 'false'
            ],
        ]);

        // Create and add platform fee invoice item
        \Stripe\InvoiceItem::create([
            'customer' => $userBrand->stripe_id,
            'amount' => intval($platformFee * 100),
            'currency' => 'eur',
            'invoice' => $platformFeeInvoice->id,
            'description' => 'Platform fee (non-refundable) for Campaign: ' . $influencers[0]->compaign_id,
        ]);

        // Create and add the VAT invoice item
        \Stripe\InvoiceItem::create([
            'customer' => $userBrand->stripe_id,
            'amount' => intval($platformVatAmount * 100),
            'currency' => 'eur',
            'invoice' => $platformFeeInvoice->id,
            'description' => 'VAT (19%) on Platform Fee',
        ]);

        // Now finalize and pay explicitly
        $platformFeeInvoiceFinalized = $platformFeeInvoice->finalizeInvoice();
        $paidPlatformInvoice = $platformFeeInvoiceFinalized->pay([
            'payment_method' => $stripePaymentMethodId,
        ]);

        // Retrieve the charge object to get the receipt URL
        $platformCharge = \Stripe\Charge::retrieve($paidPlatformInvoice->charge);
        // Store platform fee invoice in local database
        Invoice::create([
            'user_id' => Auth::id(),
            'card_token' => $paidPlatformInvoice->payment_intent,
            'charge_id' => $paidPlatformInvoice->charge,
            'campaign_id' => $influencers[0]->compaign_id,
            'influencer_request_detail_id' => null,
            'influencer_id' => null,
            'payment_type' => 'Platform_Payment',
            'payment_amount' => $platformTotal,
            'receipt' => $platformCharge->receipt_url,
            'vat_included' => true,
            'payment_status' => $paidPlatformInvoice->status,
            'paid_amount' => $platformTotal,
            'description' => 'Platform_Payment for Campaign: ' . $influencers[0]->compaign_id
        ]);

        // Process payments for each influencer
        foreach ($influencers as $rowInfluencerRequestDetail) {
            if (isset($rowInfluencerRequestDetail->influencer_request_accepts)) {
                $influencerDetail = InfluencerRequestDetail::where('id', $rowInfluencerRequestDetail->id)->first();
                
                // Retrieve the influencer user ID
                $influencerUser = User::whereId($influencerDetail->influencerdetails->user_id)->first();

                $originalAmount = $rowInfluencerRequestDetail->discount_price;

                // Convert original amount to cents
                $originalAmountCents = (int)round($originalAmount * 100);

                // Default VAT amount in cents (0 if not a small business)
                $influencerVatAmountCents = 0;

                $influencerStripeAccountId = $rowInfluencerRequestDetail->influencerdetails->user->stripeAccount->stripe_user_id;

                // Update service provider (Influencer) account settings
                $cityName = '';
                $stateName = '';
                if (!empty($influencerUser->city) && is_numeric($influencerUser->city)) {
                    $city = City::find($id = $influencerUser->city);
                    if (!empty($city->name)) {
                        $cityName = $city->name;
                    }

                    if (!empty($city->state_id) && is_numeric($city->state_id)) {
                        $state = State::find($city->state_id);
                        if (!empty($state->state)) {
                            $stateName = $state->state;
                        }
                    }
                }

                $influencerBusinessProfile = [
                    'business_profile' => [
                        'name' => $influencerUser->first_name . ' ' . $influencerUser->last_name,
                        'support_address' => [
                            'line1' => $influencerUser->street,
                            // 'city' => $influencerUser->city,
                            // 'state' => $influencerUser->state,
                            'postal_code' => $influencerUser->zip_code,
                            'country' => 'DE', // $influencerUser->country, // TODO This is numerical code in the database, need to find the two-letter iso code for country
                        ],
                    ]
                ];

                if ($cityName != '') {
                    $influencerBusinessProfile['business_profile']['support_address']['city'] = $cityName;
                }

                if ($stateName != '') {
                    $influencerBusinessProfile['business_profile']['support_address']['state'] = $stateName;
                }

                if (!empty($influencerUser->company_name)) {
                    $influencerBusinessProfile['business_profile']['name'] = $influencerUser->company_name;
                }

                $influencerStripeAccount = \Stripe\Account::update($influencerStripeAccountId, $influencerBusinessProfile);

                if (!$influencerUser->is_small_business_owner) {
                    // Calculate 19% VAT on the influencer amount
                    $influencerVatAmountCents = (int) round($originalAmountCents * 0.19);

                    InfluencerRequestDetail::where('id', $rowInfluencerRequestDetail->id)
                        ->update([
                            'vat_included' => true
                        ]);
                }

                // Total amount including VAT
                $totalAmountCents = $originalAmountCents + $influencerVatAmountCents;

                // Calculate 20% commission on the original
                $commissionRate = 0.20;
                $commissionAmountCents = (int)round($originalAmountCents * $commissionRate);


                // Calculate 19% VAT on the commission amount
                $commissionVatAmountCents = (int)round($commissionAmountCents * 0.19);

                // Total commission amount including VAT
                $totalCommissionCents = $commissionAmountCents + $commissionVatAmountCents;

                // Calculate the influencer's final amount
                $influencerAmountCents = $totalAmountCents - $totalCommissionCents;

                // Convert back to decimals for display
                $originalAmountFormatted = number_format($originalAmountCents / 100, 2);
                $influencerVatFormatted = number_format($influencerVatAmountCents / 100, 2);
                $totalAmountFormatted = number_format($totalAmountCents / 100, 2);
                $commissionFormatted = number_format($totalCommissionCents / 100, 2);
                $influencerAmountFormatted = number_format($influencerAmountCents / 100, 2);

                // Create invoice with transfer data
                $invoice = \Stripe\Invoice::create([
                    'customer' => $userBrand->stripe_id,
                    'auto_advance' => false,
                    'collection_method' => 'charge_automatically',
                    'transfer_data' => [
                        'destination' => $influencerStripeAccountId,
                        'amount' => intval($totalAmountFormatted * 100),
                    ],
                    // 'application_fee_amount' => intval($commissionAmount * 100),
                    'on_behalf_of' => $influencerStripeAccountId,
                    'metadata' => [
                        'type' => 'influencer_payment',
                        'refundable' => 'true'
                    ],
                ]);

                // Add invoice item
                \Stripe\InvoiceItem::create([
                    'customer' => $userBrand->stripe_id,
                    'amount' => intval($originalAmountFormatted * 100),
                    'currency' => 'eur',
                    'invoice' => $invoice->id,
                    'description' => 'Influencer Service for Campaign: ' . $rowInfluencerRequestDetail->compaign_id,
                ]);

                // If VAT applies, add it as a separate invoice item
                if ($influencerVatFormatted > 0) {
                    \Stripe\InvoiceItem::create([
                        'customer' => $userBrand->stripe_id,
                        'amount' => intval($influencerVatFormatted * 100),
                        'currency' => 'eur',
                        'invoice' => $invoice->id,
                        'description' => '19% VAT on Influencer Service for Campaign: ' . $rowInfluencerRequestDetail->compaign_id,
                    ]);
                }

                // Finalize and pay explicitly
                $invoice = $invoice->finalizeInvoice();
                // TODO handle for payment failure
                // IMPORTANT!!!
                $paidInvoice = $invoice->pay([
                    'payment_method' => $stripePaymentMethodId,
                ]);

                // Retrieve the charge object to get the receipt URL
                $charge = \Stripe\Charge::retrieve($paidInvoice->charge);

                // Store payment details
                $paymentDetails = [
                    'intent_id' => $paidInvoice->payment_intent,
                    'charge_id' => $paidInvoice->charge,
                    'account_id' => $influencerStripeAccountId,
                    'amount' => $formData['amount'],
                    'total_amount' => $totalAmountFormatted,
                    'influencer_amount' => $totalAmountFormatted,
                    'platform_amount' => $commissionFormatted,
                    'cash_out_amount' => $influencerAmountFormatted,
                    'receipt_url' => $charge->receipt_url,
                ];

                // Create local invoice record
                $influencerInvoice = Invoice::create([
                    'user_id' => Auth::id(),
                    'card_token' => $paidInvoice->payment_intent,
                    'charge_id' => $paidInvoice->charge,
                    'campaign_id' => $rowInfluencerRequestDetail->compaign_id,
                    'influencer_request_detail_id' => $rowInfluencerRequestDetail->id,
                    'influencer_id' => $rowInfluencerRequestDetail->influencerdetails->user_id,
                    'payment_type' => 'Influencer_Payment',
                    'payment_amount' => $totalAmountFormatted,
                    'payment_details' => json_encode($paymentDetails),
                    'receipt' => $charge->receipt_url,
                    'payment_status' => $paidInvoice->status,
                    'paid_amount' => $totalAmountFormatted,
                    'description' => 'Influencer_Payment for Campaign: ' . $rowInfluencerRequestDetail->compaign_id,
                ]);

                InfluencerRequestDetail::where('id', $rowInfluencerRequestDetail->id)
                    ->update([
                        'invoice_id' => $influencerInvoice->id,
                        'refund_txn_id' => $paidInvoice->payment_intent,
                        'platform_amount' => $commissionFormatted,
                        'influencer_amount' => $totalAmountFormatted,
                        'cash_out_amount' => $influencerAmountFormatted,
                        'payment_status' => InfluencerRequestDetail::STATUS_NEW,
                    ]);

                dispatch(new NewStartCampaignInfluencer($influencerUser, $influencerDetail));
                $influencerUser->notify(new StartCampaignInfluencer($influencerUser, $influencerDetail));
            }
        }

        InfluencerRequestDetail::where('compaign_id', $formData['compaign_id'])->where('status', 'Cancelled')->update(['status' => '2', 'read_status' => 'start_campaign', 'read_at' => NULL]);
        Campaign::where('campaign_id', $formData['compaign_id'])->update(['has_started' => True]);
        return redirect('/active-campaigns#' . $formData['compaign_id'])->with('success', 'Campaign starts successfully.');
    }

    public function updateInfluencer(Request $request)
    {

        $formData = request()->except(['_token', 'Update']);

        $InfluencerRequest = InfluencerRequestDetail::where('compaign_id', $request->compaign_id)->get();

        // loops through all influencer requests
        // checks if the iterating influencer is NOT in selectInfluencer and if it is not then
        // delete the influencer request from the table
        foreach ($InfluencerRequest as $influencer) {
            if ($request->selectInfluencer != '') {
                if (!in_array($influencer->influencer_detail_id, $request->selectInfluencer)) {
                    if (is_array($request->selectInfluencerJoin)) {
                        foreach ($request->selectInfluencerJoin as $joined) {
                            if ($influencer->influencer_detail_id != $joined) {
                                InfluencerRequestDetail::where('compaign_id', $request->compaign_id)->where('influencer_detail_id', $influencer->influencer_detail_id)->delete();
                            }
                        }
                    } else {
                        InfluencerRequestDetail::where('compaign_id', $request->compaign_id)->where('influencer_detail_id', $influencer->influencer_detail_id)->delete();
                    }
                }
            } else {
                InfluencerRequestDetail::where('compaign_id', $request->compaign_id)->where('influencer_detail_id', $influencer->influencer_detail_id)->delete();
            }
        }


        $Influencers = InfluencerRequestDetail::where('compaign_id', $request->compaign_id)->pluck('influencer_detail_id')->toArray();

        $row = $InfluencerRequest[0];
        $curent_tot = $row->total_amount;



        // code for removed influencer
        $InfluencerRequestAmt = InfluencerRequestDetail::where('compaign_id', $request->compaign_id)->get();
        $tot_amt1 = 0;
        foreach ($InfluencerRequestAmt as $influencerAmt) {
            $InfluencerDetail = InfluencerDetail::where('id', $influencerAmt->influencer_detail_id)->first();
            $user = User::where('id', $InfluencerDetail->user_id)->first();
            $prices = AdvertisingMethodNewPrice::where('user_id', $InfluencerDetail->user_id)->where('media', $influencerAmt->media)->where('type', $influencerAmt->post_type)->first();
            $tot_amt1 = $tot_amt1  + $prices->type_price;

            if ($user->is_small_business_owner == 0) {
                $tot_amt1 = $tot_amt1 + ($prices->type_price * 0.19);
            }
        }

        $comission = ($tot_amt1 * 5) / 100;
        if ($comission < 2) {
            $comission = 2;
        }
        $comission = $comission + ($comission * 0.19);
        $curent_tot1 = $tot_amt1 + $comission;

        $InfluencerR = InfluencerRequestDetail::where('compaign_id', $formData['compaign_id']);
        $InfluencerR->update(['total_amount' => $curent_tot1]);
        // code for added influencer

        $tot_amt = $curent_tot1 - $comission;
        if ($request->selectInfluencer != '') {
            if ($InfluencerRequest->count() >= 50) {
                return redirect()->back()->with('error', 'Maximum number of influencer limit reached.');
            }
            $selectInfluencer = $request->selectInfluencer;

            $totalPrice = 0;

            foreach ($selectInfluencer as $influencer) {
                $InfluencerDetail = InfluencerDetail::where('id', $influencer)->first();
                $user = User::whereId($InfluencerDetail->user_id)->first();
                $influencerRequestDetail =  InfluencerRequestDetail::where('compaign_id', $request->compaign_id)->where('influencer_detail_id', $influencer)->first();
                if (!$influencerRequestDetail) {
                    $influencerRequestDetail = $row;
                }

                if (!in_array($influencer, $Influencers)) {
                    if ($influencer != '') {
                        $prices = AdvertisingMethodNewPrice::where('user_id', $InfluencerDetail->user_id)->where('media', $influencerRequestDetail->media)->where('type', $influencerRequestDetail->post_type)->first();
                        $totalPrice = $tot_amt  + $prices->type_price;

                        if ($user->is_small_business_owner == 0) {
                            $totalPrice = $totalPrice + ($prices->type_price * 0.19);
                        }

                        $comission2 = ($totalPrice * 5) / 100;
                        if ($comission2 < 2) {
                            $comission2 = 2;
                        }
                        $comission2 = $comission2 + ($comission2 * 0.19);
                        $curent_tot = $totalPrice + $comission2;

                        $formData['current_price'] = ($prices->type_price * 80) / 100;

                        InfluencerRequestDetail::create([
                            'user_id' => $InfluencerDetail->user_id,
                            'influencer_detail_id' => $InfluencerDetail->id,
                            'advertising' =>  $influencerRequestDetail->advertising,
                            'media' =>  $influencerRequestDetail->media,
                            'name' =>  $influencerRequestDetail->name,
                            'hashtags' =>  $influencerRequestDetail->hashtags,
                            'mentions' =>  $influencerRequestDetail->mentions,
                            'social' =>  $influencerRequestDetail->social,
                            'site' => $influencerRequestDetail->site,
                            'time' => '7',
                            'current_price' => ($prices->type_price * 80) / 100,
                            'discount_price' =>  $prices->type_price,
                            'total_amount' => $curent_tot,
                            'compaign_id' => $influencerRequestDetail->compaign_id,
                            'compaign_title' => $influencerRequestDetail->compaign_title,
                            'influencer_price' => $influencerRequestDetail->influencer_price,
                            'file' => $influencerRequestDetail->file,
                            'task' => $influencerRequestDetail->task,
                            'post_type' => $influencerRequestDetail->post_type,
                            'post_content_type' => $influencerRequestDetail->post_content_type
                        ]);

                        Log::info($curent_tot);

                        $customer = User::whereId($influencerRequestDetail->user_id)->first();
                        $formData['compaign_title'] = $influencerRequestDetail->compaign_title;
                        dispatch(new NewRequestInfluencer($user, $customer, $formData));
                        $user->notify(new RequestInfluencer($user, $customer, $formData));
                    }
                }
            }
        }

        $CampaignInfluencers =  InfluencerRequestDetail::where('compaign_id', $request->compaign_id)->where('status', null)->get();
        $total_VAT = 0;
        $total_amount = 0;
        foreach ($CampaignInfluencers as $CampaignInfluencer) {
            if (
                (isset($CampaignInfluencer->influencer_request_accepts->request) &&
                    $CampaignInfluencer->influencer_request_accepts->request == 1) ||
                !isset($CampaignInfluencer->influencer_request_accepts->request)
            ) {
                $total_amount = $total_amount + $CampaignInfluencer->discount_price;

                $user = User::find(
                    $CampaignInfluencer->influencerdetails->user->id,
                );

                $VAT_value = $user->is_small_business_owner
                    ? 0
                    : $CampaignInfluencer->discount_price * 0.19;

                $total_VAT += $VAT_value;
            }
        }
        $platform_fee = ($total_amount * 5) / 100;
        if ($platform_fee < 2) {
            $platform_fee = 2;
        }
        $total_VAT = $total_VAT + $platform_fee * 0.19;

        $total_campaign_amount = $total_amount + $platform_fee + $total_VAT;

        Campaign::where('campaign_id', $request->compaign_id)->update(['total_amount' => $total_campaign_amount]);

        return back()->with('success', 'Request updated successfully.');
    }

    public function requestForm(Request $request)
    {

        $formData = request()->except(['_token']);
        $influencer_request_accept = InfluencerRequestAccept::where('influencer_request_detail_id', $formData['influencer_request_detail_id'])->first();
        if (!isset($influencer_request_accept)) {

            $request = InfluencerRequestAccept::create(
                [
                    'user_id' => Auth::id(),
                    'influencer_request_detail_id' => $formData['influencer_request_detail_id'],
                    'status' => 1
                ]
            );
        }
        $influencer_request_accept = InfluencerRequestAccept::where('influencer_request_detail_id', $formData['influencer_request_detail_id'])->first();
        $influencerDetail = InfluencerRequestDetail::where('id', $influencer_request_accept->influencer_request_detail_id)->first();
        $status = '';
        if (isset($formData['accept'])) {
            $status = 'Accepted';
            $influencer_request_accept->update(['request' => 1]);
        } else {
            $status = 'Rejected';
            $influencer_request_accept->update(['request' => 0]);
            //  $influencerDetail->update(['review' => 0, 'finish' => 0, 'refund_reason' => $status]);
            $influencerDetail->update(['review' => 0, 'refund_reason' => $status]);
        }
        $customer = User::whereId($influencerDetail->user_id)->first();




        $results = AdminGamification::where('select_type', 'Point-Rules')->first();
        // <!-- quick_response -->
        $created_date = $influencerDetail->created_at;
        $influencer_request_accepts = InfluencerRequestAccept::where('influencer_request_detail_id', $influencerDetail->id)->where('user_id', Auth::id())->first();
        if ($influencer_request_accepts != '') {
            $created_at = strtotime($influencerDetail->created_at);
            $updated_at = strtotime($influencer_request_accepts->created_at);
            $datediff = $updated_at - $created_at;
            $days =  round($datediff / (60 * 60 * 24));
            if (isset($influencer_request_accepts) && $days <= 1     && $influencerDetail->refund_reason == null) {
                // $points =$points + $results->quick_response ;
                Statistic::create([
                    'user_id' => Auth::id(),
                    'points' => $results->quick_response,
                    'type' => '1',
                    'title' =>    '[' . $influencerDetail->compaign_id . ']</br>' . $results->quick_response . ' points gained for responding to a request very fast',
                    'date' => date('Y-m-d H:i:s'),
                ]);
            }
            // <!-- points_deadlines -->
            if (isset($influencer_request_accepts) && $days > 2   && $influencerDetail->refund_reason == null) {
                // $points =$points - $results->points_deadlines ;
                Statistic::create([
                    'user_id' => Auth::id(),
                    'points' => $results->points_deadlines,
                    'type' => '0',
                    'title' => '[' . $influencerDetail->compaign_id . ']</br>' . $results->points_deadlines . ' points lost for not submitting on time',
                    'date' => date('Y-m-d H:i:s'),
                ]);
            }
        }




        $influencerDetailAll = InfluencerRequestDetail::where('compaign_id', $influencerDetail->compaign_id)->get();
        $count = 0;
        foreach ($influencerDetailAll as $all) {
            $influencerAcceptAll = InfluencerRequestAccept::where('influencer_request_detail_id', $all->id)->where('request', '1')->first();
            if (isset($influencerAcceptAll)) {
                $count++;
            }
        }

        if ($influencerDetailAll->count() == $count) {

            dispatch(new NewallInfluencersHaveCheckedYourRequest($customer, $influencer_request_accept, $influencerDetail));
            $customer->notify(new allInfluencersHaveCheckedYourRequest($customer, $influencer_request_accept, $influencerDetail));
        // } else if ($influencerDetailAll->count() / 2 <= $count) {
        //     $influencer = User::whereId($influencerDetail->influencerdetails->user_id)->first();
        //     dispatch(new NewyourCampaignisMovingForward($customer, $influencerDetail, $influencer));
        } else if ($count == 1) {

            dispatch(new NewRequestAcceptStatusInfluencer($customer, $influencer_request_accept, $influencerDetail, $status));
            $customer->notify(new RequestAcceptStatusInfluencer($customer, $influencer_request_accept, $influencerDetail, $status));
        }

        $CampaignInfluencers =  InfluencerRequestDetail::where('compaign_id', $influencerDetail->compaign_id)->where('status', null)->get();
        $total_VAT = 0;
        $total_amount = 0;
        foreach ($CampaignInfluencers as $CampaignInfluencer) {
            if (
                (isset($CampaignInfluencer->influencer_request_accepts->request) &&
                    $CampaignInfluencer->influencer_request_accepts->request == 1) ||
                !isset($CampaignInfluencer->influencer_request_accepts->request)
            ) {
                $total_amount = $total_amount + $CampaignInfluencer->discount_price;

                $user = User::find(
                    $CampaignInfluencer->influencerdetails->user->id,
                );

                $VAT_value = $user->is_small_business_owner
                    ? 0
                    : $CampaignInfluencer->discount_price * 0.19;

                $total_VAT += $VAT_value;
            }
        }
        $platform_fee = ($total_amount * 5) / 100;
        if ($platform_fee < 2) {
            $platform_fee = 2;
        }
        $total_VAT = $total_VAT + $platform_fee * 0.19;

        $total_campaign_amount = $total_amount + $platform_fee + $total_VAT;
        Campaign::where('campaign_id', $influencerDetail->compaign_id)->update(['total_amount' => $total_campaign_amount]);

        return back()->with('success', ' Request ' . $status . ' Successfully.');
    }

    public function campaignCancel($campaign_id)
    {
        $details = InfluencerRequestDetail::whereCompaignId($campaign_id)->get();

        foreach ($details as $detail) {
            $detail->status = 'Cancelled';
            $detail->refund_reason = 'Cancelled By Customer';
            $detail->save();

            $InfluencerDetail = InfluencerDetail::where('id', $detail->influencer_detail_id)->first();
            $influencer = User::whereId($InfluencerDetail->user_id)->first();

            dispatch(new NewRequestCancelInfluencer($influencer, Auth::user(), $detail));
            $influencer->notify(new RequestCancelInfluencer($influencer, Auth::user(), $detail));
        }

        return response()->json(['status' => 'Request cancelled successfully']);
    }
}
