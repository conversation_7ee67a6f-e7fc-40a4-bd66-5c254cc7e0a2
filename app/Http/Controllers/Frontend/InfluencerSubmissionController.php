<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\InfluencerRequestDetail;
use App\Models\SocialConnect;
use App\Models\SocialPost;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Exception;
use App\Models\User;
use App\Models\AdminGamification;
use App\Models\Statistic;
use App\Models\SocialKeys;
use App\Jobs\NewConfirmPostInfluencer;
use App\Jobs\NewtheInfluencersHaveDoneTheirJob;
use App\Notifications\ConfirmPostInfluencer;
use GuzzleHttp\Client;
use Storage;


class InfluencerSubmissionController extends Controller
{
    /**
     * This method handles all influencer submission from the front-end
     *
     * @param mixed $id
     * @return void
     */
    public function initiateInfluencerCampaignSubmission($id)
    {
        try {
            $influencerRequestDetail = InfluencerRequestDetail::where('id', $id)->first();
            $type = '';

            if (!$influencerRequestDetail) {
                \Log::error('initiateInfluencerCampaignSubmission: InfluencerRequestDetail not found', [
                    'id' => $id,
                    'auth_id' => Auth::id(),
                ]);
                throw new \Exception('InfluencerRequestDetail not found for the given id.');
            }

            $influencerUserId = null;
            if (
                empty($influencerRequestDetail->influencerdetails) ||
                empty($influencerRequestDetail->influencerdetails->user_id)
            ) {
                $influencerUserId = Auth::id();
                \Log::error('initiateInfluencerCampaignSubmission: Missing influencerdetails or user_id', [
                    'id' => $id,
                    'auth_id' => Auth::id(),
                    'influencerRequestDetail' => $influencerRequestDetail
                ]);
            } else {
                $influencerUserId = $influencerRequestDetail->influencerdetails->user_id;
            }

            if (empty($influencerUserId)) {
                \Log::error('initiateInfluencerCampaignSubmission: influencerUserId is empty', [
                    'id' => $id,
                    'auth_id' => Auth::id(),
                    'influencerRequestDetail' => $influencerRequestDetail
                ]);
                throw new \Exception('Invalid influencer user id.');
            }

            $socialConnects = SocialConnect::where('user_id', $influencerUserId)->get();
            foreach ($socialConnects as $socialConnect) {
                if (
                    $socialConnect->token != null &&
                    $socialConnect->media == $influencerRequestDetail->media
                ) {
                    if ($socialConnect->token_secret != null) {
                        if ($socialConnect->media == 'twitter') {
                            $this->influencerSubmitTwitter($socialConnect, $influencerRequestDetail);
                        }

                        if ($socialConnect->media == 'facebook') {
                            $this->influencerSubmitFacebook($socialConnect, $influencerRequestDetail);
                        }

                        if ($socialConnect->media == 'twitch') {
                            $this->influencerSubmitTwitch($socialConnect, $influencerRequestDetail);
                        }

                        if ($socialConnect->media == 'instagram') {
                            $this->influencerSubmitInstagram($socialConnect, $influencerRequestDetail);
                        }
                    }

                    if ($socialConnect->media == 'youtube') {
                        $this->influencerSubmitYoutube($socialConnect, $influencerRequestDetail);
                    }

                    if ($socialConnect->media == 'tiktok') {
                        $this->influencerSubmitTiktok($socialConnect, $influencerRequestDetail);
                    }
                }
            }

            if (empty($influencerRequestDetail->media)) {
                \Log::error('initiateInfluencerCampaignSubmission: media is empty or invalid', [
                    'id' => $id,
                    'auth_id' => Auth::id(),
                    'influencerRequestDetail' => $influencerRequestDetail
                ]);
                throw new \Exception('Invalid or missing media for influencer campaign submission.');
            }

            $validSocialPostCount = 0;

            $showTitle = $influencerRequestDetail->post_type;
            if ($influencerRequestDetail->post_content_type != '') {
                $showTitle .= " {$influencerRequestDetail->post_content_type}";
            }

            $socialPosts = SocialPost::where('user_id', Auth::id())
                ->where('media', $influencerRequestDetail->media)
                ->orderBy('published_at', 'desc')
                ->get();
            
            $renderPartials = [];
            
            foreach ($socialPosts as $socialPost) {
                $renderPartialName = '';

                $use_timezone = new \DateTimeZone(@Session::get('timezone') ? Session::get('timezone') : 'UTC');

                $published_at = \DateTime::createFromFormat('Y-m-d H:i:s', $socialPost->published_at, $use_timezone);
                $published_at = $published_at->format('Y-m-d H:i:s');

                $created_at = \DateTime::createFromFormat('Y-m-d H:i:s', $influencerRequestDetail->created_at, $use_timezone);
                $created_at = $created_at->format('Y-m-d H:i:s');

                $now = new \DateTime('now', $use_timezone);
                $now = $now->format('Y-m-d H:i:s');

                // The post time conditions do not met, so we do not consider this
                // social post posted for this campaign.
                if (!($published_at >= $created_at && $published_at <= $now)) {
                    continue;
                }

                if ($influencerRequestDetail->post_type == 'Boost me') {
                    $renderPartialName = 'components.partials.influencer.submission.socialpost.post-type-boost-me';
                } elseif ($influencerRequestDetail->post_type == 'Reaction video') {
                    if ($socialPost->influencer_request_accept_id == '' && isset($socialPost->type) && $socialPost->type == 'video') {
                        $renderPartialName = 'components.partials.influencer.submission.socialpost.post-type-reaction-video';
                    }

                    if ($socialPost->influencer_request_accept_id == 'reel' && $influencerRequestDetail->advertising == 'Reel') {
                        $renderPartialName = 'components.partials.influencer.submission.socialpost.post-type-reaction-video';
                    }

                    if (
                        $socialPost->influencer_request_accept_id == 'story' &&
                        $socialPost->type == 'video' &&
                        $influencerRequestDetail->advertising == 'Story - Video'
                    ) {
                        $renderPartialName = 'components.partials.influencer.submission.socialpost.post-type-reaction-video';
                    }
                } elseif ($influencerRequestDetail->post_type == 'Survey') {
                    if (
                        $socialPost->influencer_request_accept_id == 'story' ||
                        $socialPost->influencer_request_accept_id == 'reel'
                    ) {
                        $renderPartialName = 'components.partials.influencer.submission.socialpost.post-type-survey';
                    }
                }

                if ($renderPartialName != '') {
                    $validSocialPostCount += 1;
                    $renderPartials[$renderPartialName] = $socialPost;
                }
            }
            
            return \Response::json(
                \View::make(
                    'components.partials.influencer.submission.initiate',
                    compact('socialPosts', 'influencerRequestDetail', 'validSocialPostCount', 'renderPartials', 'showTitle')
                )->render()
            );

        } catch (Exception $e) {
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

    public function confirmInfluencerCampaignSubmission(Request $request)
    {
        try {
            if ($request->task != '') {
                $count_rows_updated = InfluencerRequestDetail::where('id', $request->influencer_request_id)->update([
                    'social_post_id' => $request->post_id,
                    'read_status' => 'post_submit',
                    'read_at' => NULL,
                    'tasks' => implode(',', $request->task)
                ]);
            } else {
                $count_rows_updated = InfluencerRequestDetail::where('id', $request->influencer_request_id)->update([
                    'social_post_id' => $request->post_id,
                    'read_status' => 'post_submit',
                    'read_at' => NULL
                ]);
            }

            // Sample $request data submitted
            // array(10) {
            //     ["_token"]=> string(40) "8zHhqjPsItl5dZheQMEbR9nsDuysyRyWenMsetb7"
            //     ["influencer_request_id"]=> string(3) "343"
            //     ["post_id"]=> string(2) "25"
            //     ["advertising"]=> string(4) "Reel"
            //     ["media"]=> string(9) "instagram"
            //     ["media_url"]=> string(43) "social_pics/18042587006415999_instagram.mp4"
            //     ["published_at"]=> string(19) "2025-05-24 13:26:02"
            //     ["task"]=> array(4) { [0]=> string(2) "71" [1]=> string(3) "211" [2]=> string(3) "212" [3]=> string(3) "213" }
            //     ["client_rights_terms"]=> string(2) "on"
            //     ["confirm"]=> string(6) "Submit"
            // }

            // Sample $request data for survey (with file upload)
            // Array
            // (
            //     [_token] => CDrMFGOxSIrIvAy61E01v51ZLqgYRJsw71kEfzbZ
            //     [influencer_request_id] => 503
            //     [post_id] => 1379
            //     [advertising] => Story - Picture
            //     [media] => instagram
            //     [media_url] => social_pics/18142224715389498_instagram.mp4
            //     [published_at] => 2025-06-08 09:13:52
            //     [task] => Array
            //         (
            //             [0] => 175
            //             [1] => 177
            //             [2] => 225
            //             [3] => 226
            //             [4] => 246
            //             [5] => 247
            //         )

            //     [client_rights_terms] => on
            //     [confirm] => Submit
            //     [survey_image] => Illuminate\Http\UploadedFile Object
            //         (
            //             [test:Symfony\Component\HttpFoundation\File\UploadedFile:private] => 
            //             [originalName:Symfony\Component\HttpFoundation\File\UploadedFile:private] => 9wgbmj.jpg
            //             [mimeType:Symfony\Component\HttpFoundation\File\UploadedFile:private] => image/jpeg
            //             [error:Symfony\Component\HttpFoundation\File\UploadedFile:private] => 0
            //             [hashName:protected] => 
            //             [pathName:SplFileInfo:private] => /private/var/folders/xg/kz29t8r14l11lssr39mhg0s80000gn/T/phpZDMaMF
            //             [fileName:SplFileInfo:private] => phpZDMaMF
            //         )

            // )

            $influencerRequestDetail = InfluencerRequestDetail::where('id', $request->influencer_request_id)->first();

            if (!$influencerRequestDetail || empty($influencerRequestDetail->user_id)) {
                \Log::error('InfluencerRequestDetail not found', [
                    'influencer_request_id' => $request->influencer_request_id,
                    'request_data' => $request->all(),
                    'auth_id' => Auth::id(),
                    'user_id' => $influencerRequestDetail->user_id ?? null
                ]);
                throw new \Exception('InfluencerRequestDetail not found for the given influencer_request_id.');
            }

            $customer = User::whereId($influencerRequestDetail->user_id)->first();
            if (!$customer) {
                \Log::error('Customer not found for influencerRequestDetail', [
                    'influencer_request_id' => $request->influencer_request_id,
                    'user_id' => $influencerRequestDetail->user_id,
                    'request_data' => $request->all(),
                    'auth_id' => Auth::id(),
                ]);
                throw new \Exception('Customer not found for the given influencer request.');
            }

            if (!empty($influencerRequestDetail->influencerdetails) && !empty($influencerRequestDetail->influencerdetails->user_id)) {
                $influencer = User::whereId($influencerRequestDetail->influencerdetails->user_id)->first();
            } else {
                $influencer = User::find(Auth::id());
            }

            if (!$influencer || $influencer->user_type != 'influencer') {
                \Log::info('Influencer confirmSocial fallback', [
                    'customer_id' => $customer->id ?? null,
                    'influencer_id' => $influencer->id ?? null,
                    'auth_id' => Auth::id(),
                    'influencer_user_type' => $influencer->user_type ?? null,
                ]);

                throw new Exception('Failed to load influencer details.');
            }

            if ($request->file('survey_image')) {
                $path = $request->file('survey_image')->store('social_pics');
                $influencerRequestDetail->update([
                    'insight' => $path
                ]);
            }

            $influencerRequestDetailAll = InfluencerRequestDetail::where('compaign_id', $influencerRequestDetail->compaign_id)->get();
            $count = 0;

            foreach ($influencerRequestDetailAll as $all) {
                $influencerAcceptAll = InfluencerRequestDetail::where('id', $all->id)->where('social_post_id', '!=', null)->first();
                if (isset($influencerAcceptAll)) {
                    $count++;
                }
            }

            if ($count == 1) {
                dispatch(new NewConfirmPostInfluencer($customer, $influencerRequestDetail, $influencer));
                $customer->notify(new ConfirmPostInfluencer($customer, $influencerRequestDetail, $influencer));
            } else if ($influencerRequestDetailAll->count() == $count) {
                dispatch(new NewtheInfluencersHaveDoneTheirJob($customer, $influencerRequestDetail, $influencer));
            }

            $results = AdminGamification::where('select_type', 'Point-Rules')->first();
            $created_at = strtotime($influencerRequestDetail->created_at);
            $updated_at = strtotime($influencerRequestDetail->updated_at);
            $datediff = $updated_at - $created_at;
            $days =  round($datediff / (60 * 60 * 24));
            if ($days < ($influencerRequestDetail->time) / 2  && $influencerRequestDetail->social_post_id != null && $influencerRequestDetail->refund_reason == null) {
                Statistic::create([
                    'user_id' => Auth::id(),
                    'points' => $results->points_completed_time,
                    'type' => '1',
                    'title' =>  '[' . $influencerRequestDetail->compaign_id . ']</br>' . $results->points_completed_time . ' points gained for submitting on time',
                    'date' => date('Y-m-d H:i:s'),
                ]);
            } elseif ($days <  $influencerRequestDetail->time  && $influencerRequestDetail->social_post_id != null   && $influencerRequestDetail->refund_reason == null) {
                Statistic::create([
                    'user_id' => Auth::id(),
                    'points' => $results->points_half_time,
                    'type' => '1',
                    'title' =>  '[' . $influencerRequestDetail->compaign_id . ']</br>' . $results->points_half_time . '  points gained for submitting half the time',
                    'date' => date('Y-m-d H:i:s'),
                ]);
            }

            return back()->with('success', 'Social post confirmed successfully.');
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Influencer submission handler for twitter post
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitTwitter($record, $row) {
        $type = '';
        $socialKeys = SocialKeys::first();
        $accessToken = $record->token;
        $tw_user_id = $record->social_id;
        $tw_username = $record->name;
        $redirect = config('app.url') . $socialKeys->twitter_callback_url;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.twitter.com/oauth2/token");
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt(
            $ch,
            CURLOPT_POSTFIELDS,
            http_build_query([
                'client_id' => $socialKeys->twitter_app_id,
                'client_secret' => $socialKeys->twitter_app_secret,
                'grant_type' => 'client_credentials'
            ])
        );
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);
        $content = json_decode($response);

        $url = 'https://api.twitter.com/1.1/statuses/user_timeline.json?screen_name=' . $tw_username . '&tweet_mode=extended';
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_HTTPHEADER => array(
                "Authorization: Bearer " . $content->access_token
            ),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_URL => $url
        ));
        $response = curl_exec($ch);
        curl_close($ch);
        $data_twitter = json_decode($response);

        foreach ($data_twitter as $data_row) {
            $social = SocialPost::where('user_id', $record->user_id)->where('media', 'twitter')->where('post_id', $data_row->id)->first();

            if (date('Y-m-d', strtotime($data_row->created_at)) >= date('Y-m-d', strtotime($row->created_at)) &&  date('Y-m-d', strtotime($data_row->created_at)) <= date('Y-m-d')) {
                $url = 'https://api.twitter.com/1.1/videos/tweet/config/' . $data_row->id . '.json';
                $ch = curl_init();
                curl_setopt_array($ch, array(
                    CURLOPT_HTTPHEADER => array(
                        "Authorization: Bearer " . $content->access_token
                    ),
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_URL => $url
                ));
                $response = curl_exec($ch);
                curl_close($ch);
                $data_twitter1 = json_decode($response);

                $views = isset($data_twitter1->track->viewCount) ? $data_twitter1->track->viewCount : '';

                $file = '';
                $type = '';
                if (isset($data_row->extended_entities->media[0]->media_url)) {
                    $link = '';
                    if ($data_row->extended_entities->media[0]->type == 'video') {
                        $type = 'video';
                        $link = $data_row->extended_entities->media[0]->video_info->variants[1]->url;
                    } elseif ($data_row->extended_entities->media[0]->type == 'photo' || $data_row->extended_entities->media[0]->type == 'animated_gif') {
                        $type = 'photo';
                        $link = $data_row->extended_entities->media[0]->media_url;
                    }
                    $fileContents = file_get_contents($link);
                    $filename = $data_row->id . '_twitter';
                    Storage::disk('public')->put('social_pics/' . $filename . ($data_row->extended_entities->media[0]->type == 'photo' ? '.jpg' : '.mp4'), $fileContents);
                    $file = 'social_pics/' . $filename . ($data_row->extended_entities->media[0]->type == 'photo' ? '.jpg' : '.mp4');
                } else {
                    $file = 'https://twitter.com/' . $record->name . '/status/' . $data_row->id;
                }

                if (isset($social)) {
                    $social->update([
                        'text' => $data_row->full_text,
                        'link' => $file,
                        'type' => $type,
                        'published_at' => date('Y-m-d H:i:s', strtotime($data_row->created_at)),
                        'thumbnail' => 'https://twitter.com/' . $record->name . '/status/' . $data_row->id,
                        'like' => $data_row->favorite_count,
                        'comment' => $data_row->retweet_count,
                        'view' => ($views != '') ? $views : $social->view,
                    ]);
                } else {
                    SocialPost::create([
                        'user_id' => $record->user_id,
                        'media' =>  'twitter',
                        'post_id' => $data_row->id,
                        'text' => $data_row->full_text,
                        'link' => $file,
                        'type' => $type,
                        'published_at' => date('Y-m-d H:i:s', strtotime($data_row->created_at)),
                        'thumbnail' => 'https://twitter.com/' . $record->name . '/status/' . $data_row->id,
                        'like' => $data_row->favorite_count,
                        'comment' => $data_row->retweet_count,
                        'view' => $views,
                    ]);
                }
            }
        }
    }

    /**
     * Influencer submission handler for youtube post
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitYoutube($record, $row) {
        $type = '';
        $result = SocialKeys::first();
        try {
            $youtube_subscribers = @file_get_contents(
                'https://www.googleapis.com/youtube/v3/channels?part=statistics&id=' .
                $record->social_id .
                '&key=' . env('YOUTUBE_API_KEY')
            );
            $youtube_api_response = json_decode($youtube_subscribers, true);
            $youtube_subscribers1 = @file_get_contents(
                'https://www.googleapis.com/youtube/v3/search?order=date&part=snippet&channelId=' .
                $youtube_api_response['items'][0]['id'] .
                '&key=' . env('YOUTUBE_API_KEY')
            );

            $data_youtube = json_decode($youtube_subscribers1, true);

            $youtube_subscribers3 = file_get_contents(
                'https://yt.lemnoslife.com/channels?part=shorts&id=' .
                $youtube_api_response['items'][0]['id'] .
                '&key=' . env('YOUTUBE_API_KEY')
            );

            $data_youtube3 = json_decode($youtube_subscribers3, true);

            $mode = '';
            $count = 1;
            if ($data_youtube != '') {
                foreach ($data_youtube['items'] as $data_row) {
                    if ($count < count($data_youtube['items']) &&  isset($data_row['id']['videoId'])) {
                        $mode = '';
                        foreach ($data_youtube3['items'][0]['shorts'] as $data_row3) {
                            if (isset($data_row3['videoId'])) {
                                if ($data_row['id']['videoId'] == $data_row3['videoId']) {
                                    $mode = 'shorts';
                                }
                            }
                        }

                        $youtube_subscribers4 = file_get_contents(
                            'https://www.googleapis.com/youtube/v3/videos?part=liveStreamingDetails&id=' .
                            $data_row['id']['videoId'] .
                            '&key=' . env('YOUTUBE_API_KEY')
                        );

                        $data_youtube4 = json_decode($youtube_subscribers4, true);

                        if (isset($data_youtube4['items'][0]['liveStreamingDetails'])) {
                            $mode = 'livestream';
                        }

                        $youtube_subscribers2 = file_get_contents(
                            'https://www.googleapis.com/youtube/v3/videos?part=statistics&id=' .
                            $data_row['id']['videoId'] .
                            '&key=' . env('YOUTUBE_API_KEY')
                        );

                        $data_youtube2 = json_decode($youtube_subscribers2, true);

                        $social = SocialPost::where('user_id', $record->user_id)->where('media', 'youtube')->where('post_id', $data_row["etag"])->first();
                        $file = '';

                        if (
                            date('Y-m-d', strtotime($data_row["snippet"]["publishedAt"])) >= date('Y-m-d', strtotime($row->created_at)) &&
                            date('Y-m-d', strtotime($data_row["snippet"]["publishedAt"])) <= date('Y-m-d')
                        ) {
                            if (isset($social)) {
                                $social->update([
                                    'influencer_request_accept_id' => $mode,
                                    'text' => $data_row['snippet']['title'],
                                    'link' => 'https://www.youtube.com/watch?v=' . $data_row['id']['videoId'],
                                    'type' => 'video',
                                    'published_at' => date('Y-m-d H:i:s', strtotime($data_row["snippet"]["publishedAt"])),
                                    'like' => (
                                        isset(
                                            $data_youtube2['items'][0]['statistics']['likeCount']) &&
                                            $data_youtube2['items'][0]['statistics']['likeCount'] > 0
                                        ) ? $data_youtube2['items'][0]['statistics']['likeCount'] : 0,
                                    'view' => (
                                        isset(
                                            $data_youtube2['items'][0]['statistics']['viewCount']) &&
                                            $data_youtube2['items'][0]['statistics']['viewCount'] > 0
                                        ) ? $data_youtube2['items'][0]['statistics']['viewCount'] : 0,
                                    'comment' => (
                                        isset(
                                            $data_youtube2['items'][0]['statistics']['commentCount']) &&
                                            $data_youtube2['items'][0]['statistics']['commentCount'] > 0
                                        ) ? $data_youtube2['items'][0]['statistics']['commentCount'] : 0,
                                ]);
                            } else {
                                SocialPost::create([
                                    'influencer_request_accept_id' => $mode,
                                    'user_id' => $record->user_id,
                                    'media' =>  'youtube',
                                    'post_id' => $data_row["etag"],
                                    'text' => $data_row['snippet']['title'],
                                    'link' => 'https://www.youtube.com/watch?v=' . $data_row['id']['videoId'],
                                    'type' => 'video',
                                    'published_at' => date('Y-m-d H:i:s', strtotime($data_row["snippet"]["publishedAt"])),
                                    'like' => (
                                        isset(
                                            $data_youtube2['items'][0]['statistics']['likeCount']) &&
                                            $data_youtube2['items'][0]['statistics']['likeCount'] > 0
                                        ) ? $data_youtube2['items'][0]['statistics']['likeCount'] : 0,
                                    'view' => (
                                        isset(
                                            $data_youtube2['items'][0]['statistics']['viewCount']) &&
                                            $data_youtube2['items'][0]['statistics']['viewCount'] > 0
                                        ) ? $data_youtube2['items'][0]['statistics']['viewCount'] : 0,
                                    'comment' => (
                                        isset(
                                            $data_youtube2['items'][0]['statistics']['commentCount']) &&
                                            $data_youtube2['items'][0]['statistics']['commentCount'] > 0
                                        ) ? $data_youtube2['items'][0]['statistics']['commentCount'] : 0,
                                ]);
                            }
                        }
                    }

                    //community polls
                    $youtube_subscribers5 = file_get_contents(
                        'https://yt.lemnoslife.com/channels?part=community&id=' . $youtube_api_response['items'][0]['id']
                    );
                    $data_youtube5 = json_decode($youtube_subscribers5, true);

                    foreach ($data_youtube5['items'] as $data_row) {
                        if (isset($data_row['id'])) {
                            $mode = 'polls';

                            $social = SocialPost::where('user_id', $record->user_id)->where('media', 'youtube')->where('post_id', $data_row["id"])->first();
                            $file = '';

                            if (isset($social)) {
                                $social->update([
                                    'influencer_request_accept_id' => $mode,
                                    'text' => $data_row['community'][0]['contentText'][0]['text'],
                                    'link' => 'https://www.youtube.com/' . $record->token_secret . '/community',
                                    'type' => '',
                                    'published_at' => date('Y-m-d H:i:s'),
                                    'like' => (isset($data_row['community'][0]['likes']) && $data_row['community'][0]['likes'] > 0) ? $data_row['community'][0]['likes'] : 0,
                                    'comment' => (isset($data_row['community'][0]['commentsCount']) && $data_row['community'][0]['commentsCount'] > 0) ? $data_row['community'][0]['commentsCount'] : 0,
                                ]);
                            } else {
                                $query = SocialPost::create([
                                    'influencer_request_accept_id' => $mode,
                                    'user_id' => $record->user_id,
                                    'media' =>  'youtube',
                                    'post_id' => $data_row['id'],
                                    'text' => $data_row['community'][0]['contentText'][0]['text'],
                                    'link' => 'https://www.youtube.com/' . $record->tokenSecret . '/community',
                                    'type' => '',
                                    'published_at' => date('Y-m-d H:i:s'),
                                    'like' => (isset($data_row['community'][0]['likes']) && $data_row['community'][0]['likes'] > 0) ? $data_row['community'][0]['likes'] : 0,
                                    'comment' => (isset($data_row['community'][0]['commentsCount']) && $data_row['community'][0]['commentsCount'] > 0) ? $data_row['community'][0]['commentsCount'] : 0,
                                ]);
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // TODO how to handle this error?
            // return back()->with('error', 'Something went wrong!');
        }
    }

    /**
     * Influencer submission handler for facebook post
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitFacebook($record, $row) {
        $type = '';
        $result = SocialKeys::first();
        $appId = $result->facebook_app_id;
        $secret = $result->facebook_app_secret;
        $redirectUri = config('app.url') . $result->facebook_callback_url;

        $url = 'https://graph.facebook.com/v18.0/' . $record->token_secret .
            '/posts?access_token=' . $record->social_id .
            '&fields=message,created_time,full_picture,from,place,' .
            'attachments{media_type,media,type,title,description,target,subattachments}';
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $data = json_decode(curl_exec($ch));
        curl_close($ch);

        if (isset($data->data)) {
            foreach ($data->data as $key => $value) {
                $url = 'https://graph.facebook.com/v18.0/' . $value->id . '?access_token=' . $record->social_id;
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $data_story = json_decode(curl_exec($ch));
                curl_close($ch);

                if (isset($data_story->story) && $data_story->story != '') {
                    $mode = 'livestream';
                } else {
                    $mode = '';
                }

                $url = 'https://graph.facebook.com/' . $value->id .
                    '/insights?metric=page_posts_impressions,post_impressions,post_reactions_like_total&' .
                    'access_token=' . $record->social_id;
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $data = json_decode(curl_exec($ch));
                curl_close($ch);

                $view = $data->data[0]->values[0]->value ? $data->data[0]->values[0]->value : 0;
                $like = $data->data[1]->values[0]->value ? $data->data[1]->values[0]->value : 0;
                $link = $value->attachments->data[0]->media->source ?
                    $value->attachments->data[0]->media->source :
                    $value->attachments->data[0]->media->image->src;

                if ($link != '') {
                    $fileContents = file_get_contents($link);
                    $filename = $value->id . '_facebook';
                    Storage::disk('public')->put('social_pics/' . $filename . ($value->attachments->data[0]->media_type == 'photo' ? '.jpg' : '.mp4'), $fileContents);
                    $file = 'social_pics/' . $filename . ($value->attachments->data[0]->media_type == 'photo' ? '.jpg' : '.mp4');
                }
                $type = (isset($value->attachments->data[0]->media_type)) ? $value->attachments->data[0]->media_type : "";

                if ($value->attachments->data[0]->type == 'question') {
                    $mode = 'polls';
                    $file = $value->attachments->data[0]->target->url ? $value->attachments->data[0]->target->url : '';
                    $type = '';
                }

                if (
                    date('Y-m-d', strtotime($value->created_time)) >= date('Y-m-d', strtotime($row->created_at)) &&
                    date('Y-m-d', strtotime($value->created_time)) <= date('Y-m-d')
                ) {
                    $social = SocialPost::where('user_id', $record->user_id)->where('media', 'facebook')->where('post_id', $value->id)->first();
                    if (isset($social)) {
                        $social->update([
                            'influencer_request_accept_id' => $mode,
                            'text' => (isset($value->message)) ? $value->message : "",
                            'link' => (isset($file)) ? $file : "",
                            'thumbnail' => 'https://www.facebook.com/' . $value->id,
                            'type' => $type,
                            'published_at' => date('Y-m-d H:i:s', strtotime($value->created_time)),
                            'view' => $view,
                            'like' => $like,
                        ]);
                    } else {
                        SocialPost::create([
                            'influencer_request_accept_id' => $mode,
                            'user_id' => $record->user_id,
                            'media' =>  'facebook',
                            'post_id' => $value->id,
                            'text' => (isset($value->message)) ? $value->message : "",
                            'link' => (isset($file)) ? $file : "",
                            'thumbnail' => 'https://www.facebook.com/' . $value->id,
                            'type' => $type,
                            'published_at' => date('Y-m-d H:i:s', strtotime($value->created_time)),
                            'view' => $view,
                            'like' => $like,
                        ]);
                    }
                }
            }
        }
    }

    /**
     * Influencer submission handler for tiktok post
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitTiktok($record, $row) {
        $type = '';
        $result =  SocialKeys::first();
        $appId = $result->tiktok_app_id;
        $secret = $result->tiktok_app_secret;

        $videosApi = "https://open.tiktokapis.com/v2/user/info/?fields=open_id,union_id,avatar_url,follower_count";
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_HTTPHEADER => array(
                "Authorization: Bearer " . $record->token
            ),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_URL => $videosApi
        ));

        $response = curl_exec($ch);
        curl_close($ch);
        $data_tiktok = json_decode($response);

        $videos  = "https://open.tiktokapis.com/v2/video/list/?fields=" .
            "id,title,video_description,duration,cover_image_url,share_url,embed_link," .
            "create_time,like_count,view_count,share_count,comment_count";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt_array($ch, [
            CURLOPT_HTTPHEADER => [
                "Authorization: Bearer " . $record->token
            ],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_URL => $videos
        ]);
        $response = curl_exec($ch);
        curl_close($ch);
        $content = json_decode($response);

        if (isset($content->data->videos)) {
            foreach ($content->data->videos as $data_row) {
                $social = SocialPost::where('user_id', $record->user_id)->where('media', 'tiktok')->where('post_id', $data_row->id)->first();

                if (
                    date('Y-m-d', $data_row->create_time) >= date('Y-m-d', strtotime($row->created_at)) &&
                    date('Y-m-d', $data_row->create_time) <= date('Y-m-d')
                ) {
                    if (isset($social)) {
                        $social->update([
                            'text' => $data_row->title,
                            'link' => (isset($data_row->share_url)) ? $data_row->share_url : "",
                            'type' => 'video',
                            'published_at' => date('Y-m-d H:i:s', $data_row->create_time),
                            'like' => $data_row->like_count,
                            'view' => $data_row->view_count,
                            'share' => $data_row->share_count,
                            'comment' => $data_row->comment_count,
                        ]);
                    } else {
                        SocialPost::create([
                            'user_id' => $record->user_id,
                            'media' =>  'tiktok',
                            'post_id' => $data_row->id,
                            'text' => $data_row->title,
                            'link' => (isset($data_row->share_url)) ? $data_row->share_url : "",
                            'type' => 'video',
                            'published_at' => date('Y-m-d H:i:s', $data_row->create_time),
                            'like' => $data_row->like_count,
                            'view' => $data_row->view_count,
                            'share' => $data_row->share_count,
                            'comment' => $data_row->comment_count,
                        ]);
                    }
                }
            }
        }
    }

    /**
     * Influencer submission handler for twitch post
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitTwitch($record, $row) {
        $socialKeys = SocialKeys::first();
        $accessToken = $record->token;
        $type = '';

        $videosApi = 'https://api.twitch.tv/helix/videos?user_id=' . $accessToken;
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_HTTPHEADER => [
                "Accept: application/vnd.twitchtv.v5+json",
                'Client-ID: ' . $socialKeys->twitch_app_id,
                "Authorization: Bearer " . $record->token_secret
            ],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_URL => $videosApi
        ]);
        $response = curl_exec($ch);
        curl_close($ch);
        $data_twitch = json_decode($response, JSON_PRETTY_PRINT);

        if (isset($data_twitch['data'])) {
            foreach ($data_twitch['data'] as $data_row) {
                $social = SocialPost::where('user_id', $record->user_id)->where('media', 'twitch')->where('post_id', $data_row['id'])->first();
                $file =  $data_row['url'];
                if (
                    date('Y-m-d', strtotime($data_row['published_at'])) >= date('Y-m-d', strtotime($row->created_at)) &&
                    date('Y-m-d', strtotime($data_row['published_at'])) <= date('Y-m-d')
                ) {
                    if (isset($social)) {
                        $social->update([
                            'influencer_request_accept_id' => 'livestream',
                            'text' => $data_row['title'],
                            'link' => $file,
                            'type' => 'video',
                            'published_at' => date('Y-m-d H:i:s', strtotime($data_row['published_at'])),
                            'view' => $data_row['view_count'],
                        ]);
                    } else {
                        SocialPost::create([
                            'influencer_request_accept_id' => 'livestream',
                            'user_id' => $record->user_id,
                            'media' =>  'twitch',
                            'post_id' => $data_row['id'],
                            'text' => $data_row['title'],
                            'link' => $file,
                            'type' =>  'video',
                            'published_at' => date('Y-m-d H:i:s', strtotime($data_row['published_at'])),
                            'view' => $data_row['view_count'],
                        ]);
                    }
                }
            }
        }
    }

    /**
     * Influencer submission handler for instagram post
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitInstagramPost($socialConnect, $influencerRequestDetail) {
        $instagramUserId = $socialConnect->token_secret;
        $accessToken = $socialConnect->token;

        $fields = ['id', 'caption', 'media_type', 'thumbnail_url', 'media_url', 'permalink', 'timestamp'];
        $baseApiUrl = 'https://graph.facebook.com/v18.0/' . $instagramUserId . '/media?&access_token=' . $accessToken;
        $callApiUrl = $baseApiUrl . '&fields=' . implode(',', $fields);

        $ch = curl_init($callApiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $socialPostData = json_decode(curl_exec($ch));
        curl_close($ch);

        if (isset($socialPostData->data)) {
            foreach ($socialPostData->data as $socialPostDataItem) {
                $mode = '';
                if (str_contains($socialPostDataItem->permalink, '/reel/')) {
                    $mode = 'reel';
                }

                if (empty($socialPostDataItem->id)) {
                    Log::error('Instagram post missing ID during submission processing', [
                        'user_id' => $socialConnect->user_id,
                        'influencer_request_id' => $influencerRequestDetail->id,
                        'post_data' => $socialPostDataItem,
                        'permalink' => $socialPostDataItem->permalink ?? 'N/A',
                        'media_type' => $socialPostDataItem->media_type ?? 'N/A',
                        'timestamp' => $socialPostDataItem->timestamp ?? 'N/A'
                    ]);
                    continue;
                }
                
                $social = SocialPost::where('user_id', $socialConnect->user_id)->where('media', 'instagram')->where('post_id', $socialPostDataItem->id)->first();

                $metircs = ['likes', 'comments', 'shares', 'reach'];
                $insightUrl = 'https://graph.facebook.com/v18.0/' . $socialPostDataItem->id . '/insights?access_token=' . $accessToken;
                $insightUrl .= '&metric=' . implode(',', $metircs);

                $ch = curl_init($insightUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $insightData = json_decode(curl_exec($ch));
                curl_close($ch);

                $file = '';
                $media = '';

                if (isset($insightData->data)) {
                    $likes = 0;
                    $shares = 0;
                    $comments = 0;
                    $views = 0;

                    foreach ($insightData->data as $data_count) {
                        if ($data_count->name == 'reach') {
                            $shares = $data_count->values[0]->value;
                        }

                        if ($data_count->name == 'views') {
                            $views = $data_count->values[0]->value;
                        }

                        if ($data_count->name == 'comments') {
                            $comments = $data_count->values[0]->value;
                        }

                        if ($data_count->name == 'likes') {
                            $likes = $data_count->values[0]->value;
                        }
                    }
                }

                if (
                    date('Y-m-d', strtotime($socialPostDataItem->timestamp)) >= date('Y-m-d', strtotime($influencerRequestDetail->created_at)) &&
                    date('Y-m-d', strtotime($socialPostDataItem->timestamp)) <= date('Y-m-d')
                ) {
                    if (isset($socialPostDataItem->media_url)) {
                        $fileContents = file_get_contents($socialPostDataItem->media_url);
                        $filename = $socialPostDataItem->id . '_instagram';
                        Storage::disk('public')->put('social_pics/' . $filename . ($socialPostDataItem->media_type == 'IMAGE' || $socialPostDataItem->media_type == 'CAROUSEL_ALBUM' ? '.jpg' : '.mp4'), $fileContents);
                        $file = 'social_pics/' . $filename . ($socialPostDataItem->media_type == 'IMAGE' || $socialPostDataItem->media_type == 'CAROUSEL_ALBUM' ? '.jpg' : '.mp4');

                        if ($socialPostDataItem->media_type == 'IMAGE' || $socialPostDataItem->media_type == 'CAROUSEL_ALBUM') {
                            $media = 'photo';
                        } else {
                            $media = 'video';
                        }
                    }

                    if (isset($social)) {
                        $social->update([
                            'influencer_request_accept_id' => $mode,
                            'text' => $socialPostDataItem->caption,
                            'link' => (isset($file)) ? $file : "",
                            'type' => $media,
                            'published_at' => date('Y-m-d H:i:s', strtotime($socialPostDataItem->timestamp)),
                            'thumbnail' => $socialPostDataItem->permalink,
                            'like' => max($likes, $social->like),
                            'view' => max($views, $social->view),
                            'share' => max($shares, $social->share),
                            'comment' => max($comments, $social->comment),
                        ]);
                    } else {
                        SocialPost::create([
                            'influencer_request_accept_id' => $mode,
                            'user_id' => $socialConnect->user_id,
                            'media' =>  'instagram',
                            'post_id' => $socialPostDataItem->id,
                            'text' => $socialPostDataItem->caption,
                            'link' => (isset($file)) ? $file : "",
                            'type' => $media,
                            'published_at' => date('Y-m-d H:i:s', strtotime($socialPostDataItem->timestamp)),
                            'thumbnail' => $socialPostDataItem->permalink,
                            'like' => $likes,
                            'view' => $views,
                            'share' => $shares,
                            'comment' => $comments,
                        ]);
                    }
                }
            }
        }
    }

    /**
     * Influencer submission handler for instagram post type stories
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitInstagramStories($record, $row) {
        $instagramUserId = $record->token_secret;
        $accessToken = $record->token;
        $type = '';

        $url1 = 'https://graph.facebook.com/v18.0/' . $instagramUserId . '/stories?fields=id,caption,' .
            'media_type,thumbnail_url,media_url,permalink,timestamp&access_token=' . $accessToken;
        $ch1 = curl_init($url1);
        curl_setopt($ch1, CURLOPT_RETURNTRANSFER, true);
        $data1 = json_decode(curl_exec($ch1));
        curl_close($ch1);
        if (isset($data1->data)) {
            foreach ($data1->data as $key => $value) {
                $social = SocialPost::where('user_id', $record->user_id)->where('media', 'instagram')->where('post_id', $value->id)->first();
                $url = 'https://graph.facebook.com/v18.0/' .
                    $value->id . '/insights?metric=' .
                    'views,reach,replies,shares,total_interactions&access_token=' . $accessToken;
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $data = json_decode(curl_exec($ch));
                curl_close($ch);
                $file = '';
                $media = '';

                $likes = 0;
                $shares = 0;
                $comments = 0;
                $views = 0;

                if (isset($data->data)) {
                    foreach ($data->data as $data_count) {
                        if (isset($data_count->name) && $data_count->name == 'reach') {
                            $comments = $data_count->values[0]->value;
                        }

                        if (isset($data_count->name) && $data_count->name == 'views') {
                            $views = $data_count->values[0]->value;
                        }
                        
                        if (isset($data_count->name) && $data_count->name == 'total_interactions') {
                            $likes = $data_count->values[0]->value;
                        }

                        if (isset($data_count->name) && $data_count->name == 'shares') {
                            $shares = $data_count->values[0]->value;
                        }
                    }
                }

                if (
                    date('Y-m-d', strtotime($value->timestamp)) >= date('Y-m-d', strtotime($row->created_at)) &&
                    date('Y-m-d', strtotime($value->timestamp)) <= date('Y-m-d')
                ) {
                    if (isset($value->media_url)) {
                        $fileContents = file_get_contents($value->media_url);
                        $filename = $value->id . '_instagram';
                        Storage::disk('public')->put('social_pics/' . $filename . ($value->media_type == 'IMAGE' || $value->media_type == 'CAROUSEL_ALBUM' ? '.jpg' : '.mp4'), $fileContents);
                        $file = 'social_pics/' . $filename . ($value->media_type == 'IMAGE' || $value->media_type == 'CAROUSEL_ALBUM' ? '.jpg' : '.mp4');
                        if ($value->media_type == 'IMAGE' || $value->media_type == 'CAROUSEL_ALBUM') {
                            $media = 'photo';
                        } else {
                            $media = 'video';
                        }
                    }

                    $permalink = ($value->permalink != '') ? $value->permalink : 'https://www.instagram.com/stories/' . $record->name;
                    if (isset($social)) {
                        $social->update([
                            'influencer_request_accept_id' => 'story',
                            'text' => $value->caption,
                            'link' => (isset($file)) ? $file : "",
                            'type' => $media,
                            'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
                            'thumbnail' => $permalink,
                            'like' => max($likes, $social->like),
                            'view' => max($views, $social->view),
                            'share' => max($shares, $social->share),
                            'comment' => max($comments, $social->comment),
                        ]);
                    } else {
                        SocialPost::create([
                            'influencer_request_accept_id' => 'story',
                            'user_id' => $record->user_id,
                            'media' =>  'instagram',
                            'post_id' => $value->id,
                            'text' => $value->caption,
                            'link' => (isset($file)) ? $file : "",
                            'type' => $media,
                            'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
                            'thumbnail' => $permalink,
                            'like' => $likes,
                            'view' => $views,
                            'share' => $shares,
                            'comment' => $comments,
                        ]);
                    }
                }
            }
        }
    }

    /**
     * Influencer submission handler for instagram post livestream
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitInstagramLivestream($record, $row) {
        $instagramUserId = $record->token_secret;
        $accessToken = $record->token;
        $type = '';

        $url = 'https://graph.facebook.com/v18.0/' . $instagramUserId . '/live_media?fields=id,' .
            'media_type,media_product_type,media_url,permalink,owner,username,comments&access_token=' . $accessToken;
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $data1 = json_decode(curl_exec($ch));
        curl_close($ch);

        if (isset($data1->data)) {
            foreach ($data1->data as $key => $value) {
                $mode = 'livestream';
                $social = SocialPost::where('user_id', $record->user_id)->where('media', 'instagram')->where('post_id', $value->id)->first();
                $file = '';
                $media = '';

                $likes = 0;
                $shares = 0;
                $views = 0;
                $comments = isset($value->comments) ?
                    count($value->comments->data) :
                    (
                        ($row->advertising == "Story" || $row->advertising == "Story - Video") ?
                            null : 0
                    );

                $url = "https://graph.facebook.com/" . $instagramUserId . "?fields=id,name,username," .
                    "profile_picture_url,followers_count&access_token=" . $accessToken;
                $client = new Client();
                $response = $client->request('GET', $url);
                $content = $response->getBody()->getContents();
                $oAuth = json_decode($content);
                $name = ($oAuth->username) ? $oAuth->username : $oAuth->name;

                $file = "https://www.instagram.com/{$name}/live/";
                $value->timestamp = date('Y-m-d');
                $media = 'video';
                if (isset($social)) {
                    $social->update([
                        'influencer_request_accept_id' => $mode,
                        'text' => $value->caption,
                        'link' => (isset($file)) ? $file : "",
                        'type' => $media,
                        'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
                        'thumbnail' => $value->permalink,
                        'like' => (isset($likes)) ? $likes : 0,
                        'view' => (isset($views)) ? $views : 0,
                        'share' => (isset($shares)) ? $shares : 0,
                        'comment' => (isset($comments)) ? $comments : ((($row->advertising == "Story" || $row->advertising == "Story - Video") ? null : 0)),
                    ]);
                } else {
                    SocialPost::create([
                        'influencer_request_accept_id' => $mode,
                        'user_id' => $record->user_id,
                        'media' =>  'instagram',
                        'post_id' => $value->id,
                        'text' => $value->caption,
                        'link' => (isset($file)) ? $file : "",
                        'type' => $media,
                        'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
                        'thumbnail' => $value->permalink,
                        'like' => (isset($likes)) ? $likes : 0,
                        'view' => (isset($views)) ? $views : 0,
                        'share' => (isset($shares)) ? $shares : 0,
                        'comment' => (isset($comments)) ? $comments : ((($row->advertising == "Story" || $row->advertising == "Story - Video") ? null : 0)),
                    ]);
                }
            }
        }
    }

    private function influencerSubmitInstagram($socialConnect, $influencerRequestDetail) {
        $appEnv = env('APP_ENV');
        $appUrl = env('APP_URL');
        if (
            $appEnv === 'localhost' ||
            ($appEnv === 'local' && strpos($appUrl, 'dev1') !== false)
        ) {
            \App\Models\SocialPost::createFakeInstagramPost($influencerId = $record->user_id);
            return;
        }

        $this->influencerSubmitInstagramPost($socialConnect, $influencerRequestDetail);
        $this->influencerSubmitInstagramStories($socialConnect, $influencerRequestDetail);
        // $this->influencerSubmitInstagramLivestream($socialConnect, $influencerRequestDetail);
    }
}
