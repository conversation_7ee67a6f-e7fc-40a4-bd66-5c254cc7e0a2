<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\InfluencerRequestDetail;
use App\Models\Invoice;
use App\Models\StripeAccount;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Stripe\Account;
use Stripe\AccountLink;
use Stripe\Stripe;
use Stripe\Transfer;

class OnBoardingController extends Controller
{
    public function onboardingAccountLink(Request $request){
        if (auth()->user()->user_type != 'influencer') {
            // return redirect('/');
            return response()->view('errors.' . '404', [], 404);
        }

        try{
            $stripeAccount = StripeAccount::where('user_id', auth()->user()->id)->first();

            Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));

            if (!$stripeAccount) {

                // Create a new Stripe account for the user
                $account = Account::create([
                    'type' => 'express',
                    'country' => strtoupper(auth()->user()->countries->sort),  // Germany as the account’s locale
                    'email' => auth()->user()->email,
                    'default_currency' => 'eur',  // Euros as the account’s currency
                    'settings' => [
                        'payouts' => [
                            'schedule' => [
                                'interval' => 'manual',  // Sets payouts to be manual
                            ],
                        ],
                    ],
                    'requested_capabilities' => [
                        'card_payments',
                        'transfers',
                    ],
                ]);


                // Save the new account ID and publishable key to your database
                $stripeAccount = StripeAccount::create([
                    'user_id' => auth()->user()->id,
                    'stripe_user_id' => $account->id,
                    'stripe_publishable_key' => $account->settings['dashboard']['publishable_key'] ?? null,
                ]);

            }

            // Generate an onboarding link for the user to complete the account setup
            $accountLink = AccountLink::create([
                'account' => $stripeAccount->stripe_user_id,
                'refresh_url' => url('/onboarding-account-link'),
                'return_url' => url('/connect-stripe-account'),
                'type' => 'account_onboarding',
            ]);

            // Redirect the user to complete their onboarding on Stripe's site
            return redirect($accountLink->url);

        }catch (\Exception $exception){
            return  redirect('/influencer-onboarding')->with('error', 'Something went wrong. Please try again. ' . $exception->getMessage());
        }

    }

    public function connectStripeAccount(Request $request){
        if (auth()->user()->user_type != 'influencer') {
            return response()->view('errors.' . '404', [], 404);
        }

        $user = auth()->user();
        $stripeAccount = StripeAccount::where('user_id', $user->id)->first();

        if ($stripeAccount) {
            Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));

            // Retrieve account details to check if onboarding is complete
            $account = Account::retrieve($stripeAccount->stripe_user_id);

            if ($account->details_submitted) {

                return redirect('/influencer-onboarding')->with('success', 'Your Stripe account has been connected successfully.');
            }else{
                return redirect('/influencer-onboarding')->with('success', 'Your Stripe account has been connected successfully.');
            }
        }

        return redirect('/influencer-onboarding')->with('error', 'Stripe onboarding is not complete. Please try again.');
    }

    public function goToStripeDashboard(){
        try {
            // Set Stripe API key (trimmed to prevent newline issues)
            \Stripe\Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));

            // Retrieve Stripe Account ID
            $stripeAccount = Auth::user()->stripeAccount;
            if (!$stripeAccount || !$stripeAccount->stripe_user_id) {
                return redirect('/cash-out')->with('error', 'Stripe account not found for user');
            }

            // Generate the login link
            $loginLink = Account::createLoginLink($stripeAccount->stripe_user_id);

            // Redirect to the login URL
            return redirect()->to($loginLink->url);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            return redirect('/cash-out')->with('error', $e->getMessage());
        }
    }
}
