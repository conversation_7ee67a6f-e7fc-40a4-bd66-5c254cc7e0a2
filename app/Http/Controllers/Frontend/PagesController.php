<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PagesController extends Controller
{
    public function home_login() 
    {
        return view('front-user.pages.home_login');
    }
    public function contact() 
    {
        return view('front-user.pages.contact');
    }
    public function your_statistics() 
    {
        return view('front-user.pages.your-statistics');
    }
    public function submit_campaign() 
    {
        return view('front-user.pages.submit-campaign');
    }
    public function landing_page() 
    {
        return view('front-user.pages.landing-page');
    }
    public function campaign_type() 
    {
        return view('front-user.pages.campaign-type');
    }
    public function boost_me() 
    {
        return view('front-user.pages.boost-me');
    }
    public function pages_mail_template() 
    {
        return view('front-user.pages.mail-template');
    }
    public function pages_mail_template_de() 
    {
        return view('front-user.pages.mail-template-de');
    }
    public function mail_template() 
    {
        return view('emails.sendPasswordLink-mail-template');
    }
    public function passsword_change_template() 
    {
        return view('emails.PasswordChanged-mail-template');
    }
    public function verify_mail_template()
    {
        return view('emails.verifyMail-mail-template');
    }
    public function social_already_used_template () {
        return view('emails.socialAlreadyUsed-mail-template');
    }
    public function new_contact_enquiry_template() 
    {
        return view('emails.newContactEnquiry-mail-template');
    }
    public function request_influencer_template() 
    {
        return view('emails.RequestInfluencer-mail-template');
    }
    public function request_accept_inf_template() 
    {
        return view('emails.RequestAcceptInfluencer-mail-template');
    }
    public function cancel_refund_template() 
    {
        return view('emails.CancelRefund-mail-template');
    }
    public function accept_status_inf_template() 
    {
        return view('emails.RequestAcceptStatusInfluencer-mail-template');
    }
    public function request_more_time_inf_template() 
    {
        return view('emails.RequestMoreTimeInfluencer-mail-template');
    }
   public function start_campaign_influencer_template() 
   {
    return view('emails.StartCampaignInfluencer-mail-template');
   }
   public function confirm_post_influencer_mail() 
   {
    return view('emails.ConfirmPostInfluencer-mail-template');
   }
   public function rating_submit_mail() 
   {
    return view('emails.RatingSubmit-mail-template');
   }
   public function complaint_influencer_mail() 
   {
    return view('emails.ComplaintInfluencer-mail-template');
   }
   public function complaint_admin_mail() 
   {
    return view('emails.ComplaintAdmin-mail-template');
   }
   public function paid_to_influencer_mail() 
   {
    return view('emails.paidToInfluencer-mail-template');
   }
   public function request_cancel_influencer_template() 
   {
    return view('emails.RequestCancelInfluencer-mail-template');
   }
   public function dispute_contact_admin_mail() 
   {
    return view('emails.DisputeContactAdmin-mail-template');
   }
   public function dispute_contact_user_mail() 
   {
    return view('emails.DisputeContactUser-mail-template');
   }
   public function support_contact_admin_mail() 
   {
    return view('emails.SupportContactAdmin-mail-template');
   }
   public function support_contact_user_mail() 
   {
    return view('emails.SupportContactUser-mail-template');
   }
   public function add_new_customer_mail() 
   {
    return view('emails.addNewCustomer-mail-template');
   }
   public function add_new_inf_mail() 
   {
    return view('emails.addNewInfluencer-mail-template');
   }
   public function complaint_cancelled_mail_template() 
   {
    return view('emails.complaintCancelled-mail-template');
   }
   public function complaint_update_template() 
   {
    return view('emails.ComplaintUpdate-mail-template');
   }
   public function send_activate_user_mail() 
   {
    return view('emails.SendActivateUser-mail-template');
   }
   public function payment_refund_mail() 
   {
    return view('emails.PaymentRefund-mail-template');
   }
   public function admin_user_reg_mail() 
   {
    return view('emails.adminUserRegistration-mail-template');
   }
   public function welcome_closed_beta_mail() 
   {
    return view('emails.welcomeToTheClosedBeta-mail-template');
   }
   public function welcome_closed_beta_mail_de() 
   {
    return view('emails.welcomeToTheClosedBeta_de-mail-template');
   }
   public function activation_link_de_mail() 
   {
    return view('emails.activationLink_de-mail-template');
   }
   public function reminder_campaign_time_mail() 
   {
    return view('emails.reminderCampaignTimeisRunningOut-mail-template');
   }
   public function customer_complaint_accepted_mail() 
   {
    return view('emails.customerComplaintwasAccepted-mail-template');
   }
   public function customer_complaint_rejected_mail() 
   {
    return view('emails.customerComplaintwasRejected-mail-template');
   }
   public function all_inf_checked_mail() 
   {
    return view('emails.allInfluencersHaveCheckedYourRequest-mail-template');
   }
   public function no_inf_accepted_request() 
   {
    return view('emails.noInfluencerHasAcceptedYourRequest-mail-template');
   }
   public function your_campaign_template() 
   {
    return view('emails.yourCampaignisMovingForward-mail-template');
   }
   public function the_inf_have_fun() 
   {
    return view('emails.theInfluencersHaveDoneTheirJob-mail-template');
   }
   public function campaign_completed_mail() 
   {
    return view('emails.campaignSuccessfullyCompleted-mail-template');
   }
   public function we_received_mail () 
   {
    return view('emails.weReceivedYourComplaint-mail-template');
   }
   

}
