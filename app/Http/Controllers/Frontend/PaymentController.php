<?php

/**
 * TODO
 * 
 * CANNOT FIND ANY USAGE OF THIS CLASS.
 * IS IT BEING USED?!?!?!
 * 
 * CANDIDATE FOR DEPRECATION!
 */

namespace App\Http\Controllers\Frontend;

use Illuminate\Http\Request;
use App\Notifications\newContactEnquiry;
use App\Models\User;
use App\Models\Blog;
use App\Models\Faq;
use App\Models\CmsPage;
use App\Models\Attendee;
use App\Models\ShortCourse;
use DB;
use Auth;
use App;
use App\Http\Controllers\Controller;
use PDF;
use Session;
use Stripe\AccountLink;
use Stripe\Stripe;


class PaymentController extends Controller
{
    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function payments()
    {
        \Stripe\Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));
        $stripe = new \Stripe\StripeClient(config('settings.env.STRIPE_SECRET'));
        $intent = $stripe->paymentIntents->create(
          [
            'amount' => 1099,
            'currency' => 'eur',
          ]
        );

        return view('front-user.pages.payments' , compact('intent',));
    }

    public function order(Request $request)
    {
        \Log::info($request->all());
    }

    public function success()
    {
        dd('Thank You');
    }

    public function createOrder1(Request $request)
    {
        Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));

        // If you're updating an existing connected account
        $account = \Stripe\Account::update(
            'acct_1Q7xzcLGSCmwGDEp', // Influencer's connected account ID
            [
                'settings' => [
                    'payouts' => [
                        'schedule' => [
                            'interval' => 'manual',  // This sets payouts to manual
                        ]
                    ]
                ]
            ]
        );
        dd($account);

        $account = \Stripe\Account::retrieve('acct_1Q7xzcLGSCmwGDEp');
        dd($account->requirements->currently_due);

        // Create an account link for onboarding
        $accountLink = AccountLink::create([
            'account' => 'acct_1PuH0nD0FFyQdWsM',  // The ID of the connected account
            'refresh_url' => config('app.url'),  // Where to redirect if the link expires
            'return_url' => config('app.url'),   // Where to redirect after completion
            'type' => 'account_onboarding',  // Use 'account_onboarding'
        ]);

        // Return the URL for the account link
        return $accountLink->url;
        $account = \Stripe\Account::retrieve('acct_1PuH0nD0FFyQdWsM');
        dd($account->requirements->currently_due);

        $account = \Stripe\Account::update(
            'acct_1PuH0nD0FFyQdWsM', // Influencer's connected account ID
            [
                'settings' => [
                    'payouts' => [
                        'schedule' => [
                            'interval' => 'manual',  // This sets payouts to manual
                        ]
                    ]
                ]
            ]
        );
        dd('done : '. $account);
        try {
            // Hardcoded card details for testing
            $cardDetails = [
                'number' => '****************', // Test Visa card number
                'exp_month' => 12, // Test expiration month
                'exp_year' => 2025, // Test expiration year
                'cvc' => '123', // Test CVC
            ];

            // Create a Customer or retrieve an existing one
            $customer = \Stripe\Customer::create([
                'email' => '<EMAIL>',
                'name' => 'Test Customer',
            ]);

            // Create a PaymentMethod with the hardcoded card details
            $paymentMethod = \Stripe\PaymentMethod::create([
                'type' => 'card',
                'card' => $cardDetails,
            ]);

            // Attach the PaymentMethod to the customer
            $paymentMethod->attach([
                'customer' => $customer->id,
            ]);

            $totalAmount = 10000; // Example: 100 EUR

            // 5% platform fee, minimum 2 EUR
            $platformFeeAmount = max(500, intval($totalAmount * 0.05)); // 5% of 100 EUR = 5 EUR

            // Step 1: Charge platform fee immediately
            $platformFeeIntent = \Stripe\PaymentIntent::create([
                'amount' => $platformFeeAmount,
                'currency' => 'eur',
                'customer' => $customer->id,
                'payment_method' => $paymentMethod->id,
                'off_session' => true,
                'confirm' => true,
                'description' => 'Platform fee',
            ]);

            // Generate an invoice for the platform fee
            $invoice = \Stripe\Invoice::create([
                'customer' => $customer->id,
                'auto_advance' => true, // Automatically finalize the invoice
            ]);

            // Finalize the invoice to get the hosted URL
            $finalizedInvoice = $invoice->finalizeInvoice();

            // Step 2: Create PaymentIntents for influencers
            $influencerPayments = [
                [
                    'amount' => 5000, // 50 EUR
                    'account_id' => 'acct_1PuH0nD0FFyQdWsM',
                ],
                [
                    'amount' => 4500, // 45 EUR
                    'account_id' => 'acct_1PviEHIVLjeJlQKN',
                ],
            ];

            $paymentIntents = [];

            foreach ($influencerPayments as $payment) {
                $amount = $payment['amount'];
                $accountId = $payment['account_id'];

                // 20% commission
                $commissionAmount = intval($amount * 0.20); // e.g., 20% of 50 EUR = 10 EUR
                $netAmount = $amount - $commissionAmount; // Amount sent to influencer

                // Create a PaymentIntent for each influencer
                $intent = \Stripe\PaymentIntent::create([
                    'amount' => $amount,
                    'currency' => 'eur',
                    'customer' => $customer->id,
                    'payment_method' => $paymentMethod->id,
                    'off_session' => true,
                    'capture_method' => 'manual', // Capture later
                    'application_fee_amount' => $commissionAmount,
                    'confirm' => true,
                    'transfer_data' => [
                        'destination' => $accountId, // Stripe account ID of influencer
                    ],
                    'on_behalf_of' => $accountId,
                    'metadata' => [
                        'commission_amount' => $commissionAmount,
                    ],
                    'description' => 'Influencer Payment',
                ]);

                $paymentIntents[] = [
                    'intent_id' => $intent->id,
                    'account_id' => $accountId,
                    'amount' => $amount,
                    'net_amount' => $netAmount,
                    'commission_amount' => $commissionAmount,
                ];
            }

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'order_id' => uniqid('CLICKITFAME'),
                'platform_fee_intent' => $platformFeeIntent->id,
                'platform_fee_invoice_url' => $finalizedInvoice->hosted_invoice_url, // Add platform fee invoice URL
                'payment_intents' => $paymentIntents,
                'total_amount' => $totalAmount,
            ]);

        } catch (\Stripe\Exception\ApiErrorException $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }


    public function createOrder(Request $request)
    {
        \Stripe\Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));

        try {
            // Hardcoded card details for testing
            $cardDetails = [
                'number' => '****************',
                'exp_month' => 12,
                'exp_year' => 2025,
                'cvc' => '123',
            ];

            // Check if customer exists using email
            $existingCustomers = \Stripe\Customer::all([
                'email' => '<EMAIL>',
                'limit' => 1,
            ]);

            // If the customer exists, retrieve them; otherwise, create a new one
            if (count($existingCustomers->data) > 0) {
                $customer = $existingCustomers->data[0];
            } else {
                $customer = \Stripe\Customer::create([
                    'email' => '<EMAIL>',
                    'name' => 'Test Customer',
                ]);
            }

            // Create a PaymentMethod with the hardcoded card details
            $paymentMethod = \Stripe\PaymentMethod::create([
                'type' => 'card',
                'card' => $cardDetails,
            ]);

            // Attach the PaymentMethod to the customer
            $paymentMethod->attach([
                'customer' => $customer->id,
            ]);

            $totalAmount = 10000; // Example: 100 EUR

            // 5% platform fee, minimum 2 EUR
            $platformFeeAmount = max(500, intval($totalAmount * 0.05));

            // Step 1: Create and confirm platform fee payment intent
            $platformFeeIntent = \Stripe\PaymentIntent::create([
                'amount' => $platformFeeAmount,
                'currency' => 'eur',
                'customer' => $customer->id,
                'payment_method' => $paymentMethod->id,
                'off_session' => true,
                'confirm' => true,
                'description' => 'Platform fee (non-refundable)',
                'receipt_email' => $customer->email,
                'metadata' => [
                    'type' => 'platform_fee',
                    'refundable' => 'false'
                ],
            ]);

            // Wait for the platform fee payment to be confirmed and get the charge
//            $platformFeeIntent = $this->waitForPaymentIntent($platformFeeIntent->id);
            $platformCharge = \Stripe\Charge::retrieve($platformFeeIntent->latest_charge);

            $paymentIntents = [];
            $influencerPayments = [
                [
                    'amount' => 5000,
                    'account_id' => 'acct_1PuH0nD0FFyQdWsM',
                ],
                [
                    'amount' => 4500,
                    'account_id' => 'acct_1PviEHIVLjeJlQKN',
                ],
            ];

            foreach ($influencerPayments as $payment) {
                $amount = $payment['amount'];
                $accountId = $payment['account_id'];

                // 20% commission
                $commissionAmount = intval($amount * 0.20);
                $netAmount = $amount - $commissionAmount;

                // Create and confirm payment intent for influencer
                $intent = \Stripe\PaymentIntent::create([
                    'amount' => $amount,
                    'currency' => 'eur',
                    'customer' => $customer->id,
                    'payment_method' => $paymentMethod->id,
                    'off_session' => true,
                    'confirm' => true,
                    'application_fee_amount' => $commissionAmount,
                    'transfer_data' => [
                        'destination' => $accountId,
                    ],
                    'on_behalf_of' => $accountId,
                    'description' => 'Influencer Payment',
                    'receipt_email' => $customer->email,
                    'metadata' => [
                        'type' => 'influencer_payment',
                        'refundable' => 'true'
                    ],
                ]);

                // Wait for the payment to be confirmed and get the charge
//                $intent = $this->waitForPaymentIntent($intent->id);
                $charge = \Stripe\Charge::retrieve($intent->latest_charge);

                $paymentIntents[] = [
                    'intent_id' => $intent->id,
                    'charge_id' => $charge->id,
                    'account_id' => $accountId,
                    'amount' => $amount,
                    'net_amount' => $netAmount,
                    'commission_amount' => $commissionAmount,
                    'receipt_url' => $charge->receipt_url,
                ];
            }

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'order_id' => uniqid('CLICKITFAME'),
                'platform_fee_receipt_url' => $platformCharge->receipt_url,
                'payment_receipts' => $paymentIntents,
                'total_amount' => $totalAmount,
            ]);

        } catch (\Stripe\Exception\ApiErrorException $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

// Helper method to wait for PaymentIntent to be confirmed
    private function waitForPaymentIntent($intentId, $maxAttempts = 5)
    {
        $attempts = 0;
        $intent = \Stripe\PaymentIntent::retrieve($intentId);

        while ($intent->status !== 'succeeded' && $attempts < $maxAttempts) {
            sleep(1);
            $intent = \Stripe\PaymentIntent::retrieve($intentId);
            $attempts++;
        }

        if ($intent->status !== 'succeeded') {
            throw new \Exception("Payment failed to complete after $maxAttempts attempts");
        }

        return $intent;
    }

// Add this method to handle refunds
    public function handleRefund($chargeId)
    {
        try {
            // Retrieve the charge
            $charge = \Stripe\Charge::retrieve($chargeId);

            // Check if this is a platform fee (non-refundable)
            if ($charge->metadata['type'] === 'platform_fee' &&
                $charge->metadata['refundable'] === 'false') {
                return response()->json([
                    'error' => 'Platform fees are non-refundable',
                ], 400);
            }

            // If it's an influencer payment, proceed with refund
            $refund = \Stripe\Refund::create([
                'charge' => $chargeId,
                'refund_application_fee' => true, // Refund the application fee as well
            ]);

            return response()->json([
                'success' => true,
                'refund_id' => $refund->id,
            ]);

        } catch (\Stripe\Exception\ApiErrorException $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }



}
