<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use App\Models\User;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use App\Notifications\registration;
use App\Notifications\adminUserRegistration;
use App\Models\SocialConnect;
use App\Models\SocialKeys;
use Auth;
use Socialite;
use Storage;
use GuzzleHttp\Client;
use Session;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    public function showSignupForm()
    {
        return view('auth.signup');
    }

    public function showSignupFormBrand()
    {
        Session::put('user_type', 'customer');
        Session::forget('login_error');
        return view('auth.signup');
    }
    public function showSignupFormInfluencer()
    {
        Session::put('user_type', 'influencer');
        Session::forget('login_error');
        return view('auth.signup');
    }

    protected function validator(array $data)
    {
        return Validator::make($data, [
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ], [
            'email.unique' => 'The email is already registered on this site.',
            'password.confirmed' => 'The proposed passwords do not match.'
        ]);
    }

    protected function register(Request $request)
    {


        $formData = request()->except(['_token']);

        // validate the user form data
        $validation = $this->validator($formData);

        // if validation fails
        if ($validation->fails()) {
            // redirect back to the form
            return redirect()->back()->withErrors($validation)->withInput();
        }
        // if validation passes
        $password = $formData['password'];
        $formData['password'] = Hash::make($formData['password']);

        $formData['verify_token'] = str_random(30);

        $formData['status'] = 0;
        // save the user to the database
        $user = User::create($formData);

        //Notify the user for the registration
        $user->notify(new registration($user));

        //Notify the admin for the registration
        $admin = User::whereUserType('admin')->first();
        $admin->notify(new adminUserRegistration($user, $admin));

        Auth::login($user);
        // return a view

        if ($user->user_type == 'influencer' && $user->first_name == null) {
            $redirect = "/my-profile";
        } elseif ($user->user_type == 'customer' && $user->first_name == null) {
            $redirect = "/";
        } else {
            $redirect = "/";
        }
        return redirect($redirect)->with('success', 'Registration successful! Please check your email for verification.');
    }

    ### social login api




    public function sessionUsertype(Request $request)
    {
        Session::put('user_type', $request->user_type);
    }

    public function redirectToProvider($provider)
    {
        if ($provider == 'facebook')
            return Socialite::driver($provider)->scopes(['public_profile', 'pages_show_list', 'pages_read_engagement'])->redirect();
        elseif ($provider == 'instagram')
            return Socialite::driver($provider)->scopes(['instagram_basic'])->redirect();
        else
            return Socialite::driver($provider)->redirect();
    }

    public function handleProviderCallback($provider, Request $request)
    {
        if ($request->error != '')
            return redirect('login');
        $userSocial = Socialite::driver($provider)->user();

        // return redirect('login')->with(['userSocial'=>$userSocial,'provider'=>$provider]);

        //dd($userSocial);
        $name = @$userSocial->name != '' ? @$userSocial->name : @$userSocial->nickname;
        // echo "Name: ". $name;
        // echo nl2br('<br>');
        $avatar = @$userSocial->avatar_original != '' ? @$userSocial->avatar_original : @$userSocial->avatar;
        // echo "Picture: ". $avatar;
        // echo nl2br('<br>');
        // echo "Email: ". @$userSocial->email;
        // echo nl2br('<br>');
        // echo "URL: ". @$userSocial->profileUrl;
        // echo nl2br('<br>');
        // echo "Followers: ". @$userSocial->followers_count;
        // echo nl2br('<br>');



        if (@$userSocial->email != null)
            $user = User::where('email', @$userSocial->email)->first();
        else
            $user = User::where('provider_id', @$userSocial->id)->first();
        if ($user->flag == null || $user->set_password == null) {
            Session::put('login_error', 'Your account is not activated by admin yet!');
            $url = config('app.url') . "login";
            return "<script type='text/javascript'> self.close();window.opener.location = '$url';</script>";
        } else {
            if (!$user) {
                Session::put('login_error', 'Your account is not registered. Please signup to login!');
                $url = config('app.url') . "login";
                return "<script type='text/javascript'> self.close();window.opener.location = '$url';</script>";
            } elseif ($user->email_verified_at == null) {
                Auth::login($user);
                $user->update(['email_verified_at' => date('Y-m-d')]);
            }

            if ($user->user_type == 'influencer') {
                Auth::login($user);
                $records = SocialConnect::where('user_id', $user->id)->get();
                foreach ($records as $record) {
                    if ($record->token != null &&  $record->token_secret != null && $record->media == 'twitter') {
                        try {
                            $userSocial = Socialite::driver('twitter')->userFromTokenAndSecret($record->token, $record->token_secret);
                            $record->update([
                                'followers' => @$userSocial['followers_count']
                            ]);
                        } catch (\Exception $e) {
                        }
                    }
                    if ($record->token != null && $record->media == 'youtube') {
                        try {
                            $youtube_subscribers = file_get_contents('https://www.googleapis.com/youtube/v3/channels?part=statistics&id=' . $record->token . '&key=' . env('YOUTUBE_API_KEY'));
                            $youtube_api_response = json_decode($youtube_subscribers, true);
                            // Log::info(print_r($youtube_api_response['items'][0]['statistics']));

                            $followers_count = intval($youtube_api_response['items'][0]['statistics']['subscriberCount']);

                            $record->update([
                                'followers' => $followers_count
                            ]);
                        } catch (\Exception $e) {
                        }
                    }

                    if ($record->token != null &&  $record->token_secret != null && $record->media == 'twitch') {

                        try {
                            $result = SocialKeys::first();
                            $appId = $result->twitch_app_id;
                            $secret = $result->twitch_app_secret;

                            $ch = curl_init();
                            curl_setopt($ch, CURLOPT_URL, "https://id.twitch.tv/oauth2/token");
                            curl_setopt($ch, CURLOPT_POST, 1);
                            //curl_setopt($ch, CURLOPT_POSTFIELDS, "postvar1=value1&postvar2=value2&postvar3=value3");

                            // In real life you should use something like:
                            curl_setopt(
                                $ch,
                                CURLOPT_POSTFIELDS,
                                http_build_query([
                                    'client_id' => $appId,
                                    'client_secret' => $secret,
                                    'grant_type' => 'refresh_token',
                                    'refresh_token' => $record->token_secret
                                ])
                            );
                            // Receive server response ...
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            $response = curl_exec($ch);
                            curl_close($ch);
                            $content = json_decode($response);
                            $videosApi = 'https://api.twitch.tv/helix/users/follows?to_id=' . $record->token;
                            $clientId = config('services.twitch.client_id');
                            $ch = curl_init();

                            curl_setopt_array($ch, array(
                                CURLOPT_HTTPHEADER => array(
                                    "Accept: application/vnd.twitchtv.v5+json",
                                    'Client-ID: ' . $clientId,
                                    "Authorization: Bearer " . $content->access_token
                                ),
                                CURLOPT_SSL_VERIFYPEER => false,
                                CURLOPT_RETURNTRANSFER => true,
                                CURLOPT_URL => $videosApi
                            ));
                            $response = curl_exec($ch);
                            curl_close($ch);
                            $data = json_decode($response, JSON_PRETTY_PRINT);

                            if (isset($data['total'])) {
                                $record->update([
                                    'followers' => $data['total'],
                                    'token_secret' => $content->refresh_token
                                ]);
                            }
                        } catch (\Exception $e) {
                        }
                    }

                    if ($record->token != null &&  $record->token_secret != null && $record->media == 'facebook') {
                        try {
                            $result = SocialKeys::first();
                            $appId = $result->facebook_app_id;
                            $secret = $result->facebook_app_secret;
                            $redirectUri = config('app.url') . $result->facebook_callback_url;
                            $ch = curl_init();
                            curl_setopt($ch, CURLOPT_URL, "https://graph.facebook.com/oauth/access_token");
                            curl_setopt($ch, CURLOPT_POST, 1);
                            curl_setopt(
                                $ch,
                                CURLOPT_POSTFIELDS,
                                http_build_query([
                                    'grant_type' => 'fb_exchange_token',
                                    'client_id' => $appId,
                                    'client_secret' => $secret,
                                    'fb_exchange_token' => $record->token,
                                ])
                            );

                            // Receive server response ...
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            $response = curl_exec($ch);
                            curl_close($ch);
                            $content = json_decode($response);
                            $record->update([
                                'token' => $content->access_token
                            ]);


                            $url = "https://graph.facebook.com/" . $record->token_secret . "?fields=id,name,picture,followers_count,link&access_token=" . $content->access_token;
                            $client = new Client();
                            $response = $client->request('GET', $url);
                            $content = $response->getBody()->getContents();
                            $oAuth = json_decode($content);
                            $record->update([
                                'followers' => @$oAuth->followers_count
                            ]);
                        } catch (\Exception $e) {
                        }
                    }

                    if ($record->token != null &&  $record->token_secret != null && $record->media == 'instagram') {

                        try {

                            $result = SocialKeys::first();
                            $appId = $result->instagram_app_id;
                            $secret = $result->instagram_app_secret;
                            $redirectUri = config('app.url') . $result->instagram_callback_url;
                            $url = 'https://graph.facebook.com/v2.3/me/accounts?access_token=' . $record->token;
                            $ch = curl_init();
                            CURL_SETOPT($ch, CURLOPT_URL, $url);
                            CURL_SETOPT($ch, CURLOPT_RETURNTRANSFER, 1);
                            $json = json_decode(curl_exec($ch));

                            if (isset($json->data[0]->access_token)) {
                                $access_token = $json->data[0]->access_token;
                            } else {
                                $access_token = $record->token;
                            }

                            $record->update([
                                'token' => $access_token
                            ]);
                            $url = "https://graph.facebook.com/" . $record->token_secret . "?fields=id,name,username,profile_picture_url,followers_count&access_token=" . $access_token;
                            $client = new Client();
                            $response = $client->request('GET', $url);
                            $content = $response->getBody()->getContents();
                            $oAuth = json_decode($content);
                            $record->update([
                                'followers' => @$oAuth->followers_count
                            ]);
                        } catch (\Exception $e) {
                        }
                    }

                    if ($record->token != null &&   $record->media == 'tiktok') {
                        try {
                            $videosApi = "https://open.tiktokapis.com/v2/user/info/?fields=follower_count";
                            $ch = curl_init();
                            curl_setopt_array($ch, array(
                                CURLOPT_HTTPHEADER => array(
                                    "Authorization: Bearer " . @$record->token
                                ),
                                CURLOPT_SSL_VERIFYPEER => false,
                                CURLOPT_RETURNTRANSFER => true,
                                CURLOPT_URL => $videosApi
                            ));
                            $response = curl_exec($ch);
                            curl_close($ch);
                            $oAuth = json_decode($response);
                            $record->update([
                                'followers' => $oAuth->data->user->follower_count
                            ]);
                        } catch (\Exception $e) {
                        }
                    }
                }

                if ($user->flag == 1) {
                    if ($user->activate != '2') {
                        $url = config('app.url') . "influencer-onboarding";
                    } else {
                        $url = config('app.url');
                    }
                } else {
                    $url = config('app.url');
                }
            } elseif ($user->user_type == 'customer') {
                Auth::login($user);
                $url = config('app.url');
            }
            return "<script type='text/javascript'> self.close(); window.opener.location = '$url';</script>";
        }
    }

    public function verify_user($token)
    {
        $verifyUser = User::where('verify_token', $token)->first();
        if ($verifyUser) {
            $verifyUser->email_verified_at = Date('Y-m-d H:i:s');
            $verifyUser->save();

            Auth::login($verifyUser);
            return redirect('/')->with('success', 'Email verified successfully.');
        } else {
            return redirect('/')->with('error', 'Something went wrong!.');
        }
    }

    public function sendVerificationMail($email)
    {
        $email = base64_decode($email);

        $user = User::whereEmail($email)->first();
        $user->verify_token = str_random(30);
        $user->save();

        //Notify the user for the registration
        $user->notify(new registration($user));

        return redirect('/')->with('success', 'Verification email resent. Please check your email.');
    }
}
