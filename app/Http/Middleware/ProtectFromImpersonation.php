<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ProtectFromImpersonation
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (auth()->check() && auth()->user()->isImpersonated()) {
            abort(403, 'This action is not allowed while impersonating a user.');
        }

        return $next($request);
    }
}
