<?php

namespace App\Console\Commands;

use App\Jobs\NewRequestCancelInfluencer;
use App\Models\Campaign;
use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestAccept;
use Socialite;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use App\Models\SocialKeys;
use App\Models\CampaignRequestTime;
use App\Models\InfluencerDetail;
use App\Models\User;
use App\Notifications\RequestCancelInfluencer;
use Illuminate\Support\Facades\Auth;

class TimeoutCustomerPayment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'timeoutcustomer:payment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancelled Timeout Customer Payment';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $campaignRequestTime = CampaignRequestTime::first();
        $records =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->select('influencer_request_details.*', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id',)
            ->get();

        foreach ($records as $row) {
            $created_date =  date('Y-m-d H:i:s', strtotime($row->created_at));
            // request time for request phase and 3 for payment phase
            $campaignDate = date('Y-m-d H:i:s', strtotime($created_date . ' + ' . ($campaignRequestTime->request_time + 3) . ' days'));
            $date = date('Y-m-d H:i:s');
            $seconds = strtotime($campaignDate) - strtotime($date);

            $days    = floor($seconds / 86400);
            if (!($days < 6 && $days >= 0)) {
                $campaign = Campaign::where('campaign_id', $row->compaign_id)->first();
                if (isset($campaign->has_started) && $campaign->has_started == 0 && $row->status == null) {
                    $detail = InfluencerRequestDetail::where('id', $row->id)->where('compaign_id', $row->compaign_id)->first();

                    $detail->update(["status" => 'Cancelled', 'refund_reason' => "Cancelled By Customer"]);

                    $InfluencerDetail = InfluencerDetail::where('id', $detail->influencer_detail_id)->first();
                    $influencer = User::whereId($InfluencerDetail->user_id)->first();

                    $customer = User::whereId($detail->user_id)->first();
                    Log::info('Timeout Customer payment time passed' . $detail->compaign_id);

                    dispatch(new NewRequestCancelInfluencer($influencer, $customer, $detail));
                    $influencer->notify(new RequestCancelInfluencer($influencer, $customer, $detail));
                }
            }
        }
    }
}
