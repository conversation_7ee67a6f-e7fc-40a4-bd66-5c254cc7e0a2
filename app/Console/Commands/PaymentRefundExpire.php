<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\InfluencerRequestDetail;
use App\Models\User;
use App\Notifications\PaymentRefund;
use Carbon\Carbon;
use App\Models\StripeAccount;
use App\Models\TransferInfluencer;

use App\Jobs\NewPaymentRefund;

class PaymentRefundExpire extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'paymentrefund:expire';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $details = InfluencerRequestDetail::leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )->select('influencer_request_details.*','influencer_request_accepts.request','influencer_request_accepts.request_time','influencer_request_accepts.request_time_accept' )->where('invoice_id','!=',null)->get();

      foreach ($details as $row) {
            $time = (isset($row->request_time_accept) && $row->request_time_accept == '1')?$row->request_time+$row->time:$row->time;

            $created_date =  date('Y-m-d H:i:s',strtotime($row->created_at));
            $updated_date =  date('Y-m-d H:i:s',strtotime($row->updated_at));
            $campaignDate= date('Y-m-d H:i:s', strtotime($created_date. ' + '.$time.' days'));
            $date = date('Y-m-d H:i:s');
            $seconds = strtotime($date) - strtotime($campaignDate);

            $days    = floor($seconds / 86400);
            if($days > $time && $days >=0 && $row->refund_txn_id !='' && $row->refund_reason ==''){

                // print_r($row);

                $amount = 0;
                $fieldName = $row->advertising.'_price';
                $influencer =  User::where('id',$row->influencerdetails->user_id)->first();
                // if($user->advertisingMethodPrice != null)
                // {
                //     $amount = $user->advertisingMethodPrice->$fieldName;
                // }


                $customer =  User::where('id',$row->user_id)->first();
                // $stripe_account = StripeAccount::where('user_id',$row->influencerdetails->user_id)->first();

                $txn_id = $row->invoices->charge_id ?? '';

                $amount = $row->current_price;


                if( $row->invoices->is_refunded == 0  && $txn_id!= '') {
                    \Log::info('stripe connected');

                     $row->update([ 'refund_reason'=>'Time expired','refund_payment_date'=>date('Y-m-d H:i:s')]);

                    \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));

                    try{
                        // Retrieve the charge
                        $charge = \Stripe\Charge::retrieve($txn_id);


                        // If it's an influencer payment, proceed with refund
                        $result = \Stripe\Refund::create([
                            'charge' => $txn_id,
                            'refund_application_fee' => true,  // If you want to refund the application fee
                            'reverse_transfer' => true  // This will reverse the transfer to the connected acc
                        ]);

                        $refund_success = @$result->status;
                        $refund_txn_id = @$result->id;


                      if($refund_success)
                      {
                          $row->update([ 'refund_reason'=>'Refunded On Time Expired',
                              'payment_status' => InfluencerRequestDetail::STATUS_REFUNDED,'refund_payment_date'=>date('Y-m-d H:i:s')]);
                          $row->invoices->update([
                              'is_refunded' => 1,
                              'payment_status' => 'Refunded',
                              'description' => 'Influencer Payment Refunded'
                          ]);

                          dispatch(new NewPaymentRefund($customer, $row, $influencer ));
                          $customer->notify(new PaymentRefund($customer, $row, $influencer ));
                      }

                    }catch (\Exception $e) {
                        \Log::info($e);
                    }
                }
            }
      }

        return 0;
    }
}
