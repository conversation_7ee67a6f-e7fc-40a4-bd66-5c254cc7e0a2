<?php

namespace App\Console\Commands;

use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestAccept;
use Socialite;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use App\Models\SocialKeys;
use App\Models\CampaignRequestTime;
use App\Models\User;
use App\Notifications\CancelRefund;

use App\Jobs\NewCancelRefund;
use App\Jobs\NewSubmitPhaseEnded;
use App\Models\AdminGamification;
use App\Models\Statistic;

class CancelledTimeoutInfluencerSubmit extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cancelledinfluencer:submit';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancelled Timeout Influencer Submit: Influencer did not submit during submit phase';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $campaignRequestTime = CampaignRequestTime::first();
        $records =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->leftjoin('campaigns', 'campaigns.campaign_id', '=', 'influencer_request_details.compaign_id')
            ->select('influencer_request_details.*', 'campaigns.has_started', 'campaigns.has_finished', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request', 'influencer_request_accepts.request_time', 'influencer_request_accepts.id as influencer_request_accept_id', 'influencer_request_accepts.created_at as accept_time')
            ->where('campaigns.has_started', true)
            ->where('campaigns.has_finished', false)
            ->get();
        $results = AdminGamification::where('select_type', 'Point-Rules')->first();

        foreach ($records as $row) {
            $time = (isset($row->request_time_accept) && $row->request_time_accept == '1') ? $row->request_time + $row->time : $row->time;

            $campaignDate = date('Y-m-d H:i:s', strtotime($row->accept_time ? $row->accept_time . ' + ' . $time . ' days' : 0 . ' + ' . $time . ' days'));

            $date = date('Y-m-d H:i:s');
            $seconds = strtotime($campaignDate) - strtotime($date);

            $days    = floor($seconds / 86400);
            if ($days < $time && $days > 0) {
                // TODO what to do here?
            } else {
                if ($row->refund_reason  != 'Cancelled' && $row->social_post_id == null && $row->finish != '0') {
                    if (!$row->is_paused) {
                        if ($row->social_post_id == null) {
                            $row->update(['finish' => 0, 'review' => 0]);

                            $amount = 0;
                            $fieldName = $row->advertising . '_price';
                            $influencer =  User::where('id', $row->influencerdetails->user_id)->first();

                            $customer =  User::where('id', $row->user_id)->first();
                            $txn_id = $row->invoices->charge_id ?? '';
                            $amount = $row->current_price;

                            if (isset($row->invoices->is_refunded) && $row->invoices->is_refunded == 0  && $txn_id != '') {
                                \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));
                                try {
                                    // Retrieve the charge
                                    $charge = \Stripe\Charge::retrieve($txn_id);

                                    // If it's an influencer payment, proceed with refund
                                    $result = \Stripe\Refund::create([
                                        'charge' => $txn_id,
                                        'refund_application_fee' => true,  // If you want to refund the application fee
                                        'reverse_transfer' => true  // This will reverse the transfer to the connected acc
                                    ]);

                                    $refund_success = @$result->status;
                                    $refund_txn_id = @$result->id;

                                    if ($refund_success) {
                                        $row->update([
                                            'refund_reason' => 'Refunded On Time Expired',
                                            'payment_status' => InfluencerRequestDetail::STATUS_REFUNDED,
                                            'refund_payment_date' => date('Y-m-d H:i:s')
                                        ]);
                                        $row->invoices->update([
                                            'is_refunded' => 1,
                                            'payment_status' => 'Refunded',
                                            'description' => 'Influencer Payment Refunded'
                                        ]);
                                    }

                                    Statistic::create([
                                        'user_id' => $row->influencerdetails->user_id,
                                        'points' => $results->points_deadlines,
                                        'type' => '0',
                                        'title' => '[' . $row->compaign_id . ']</br>' . $results->points_deadlines . ' points lost for not submitting on time',
                                        'date' => date('Y-m-d H:i:s'),
                                    ]);

                                    dispatch(new NewCancelRefund($customer, $row));
                                    $customer->notify(new CancelRefund($customer, $row));
                                } catch (\Exception $e) {
                                    \Log::error('Exception occurred', ['exception' => $e]);
                                }
                            }

                            dispatch(new NewSubmitPhaseEnded($influencer, $row));
                        }
                    }
                }
            }
        }
    }
}
