<?php

namespace App\Console\Commands;

use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestAccept;
use Socialite;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use App\Models\SocialKeys;
use App\Models\CampaignRequestTime;

class CancelledTimeoutCustomerRequest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cancelledcustomer:request';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancelled Timeout Customer Request';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $campaignRequestTime = CampaignRequestTime::first();
        $records =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->select('influencer_request_details.*', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request', 'influencer_request_accepts.request_time', 'influencer_request_accepts.id as influencer_request_accept_id')
            ->get();

        foreach ($records as $row) {

            $time = (isset($row->influencer_request_accepts->request_time_accept) && $row->influencer_request_accepts->request_time_accept == 1) ? $row->influencer_request_accepts->request_time + $row->time : $row->time;

            $created_date =  date('Y-m-d H:i:s', strtotime($row->created_at));
            // request time for request phase and 3 for payment phase
            $campaignDate = date('Y-m-d H:i:s', strtotime($created_date . ' + ' . ($campaignRequestTime->request_time) . ' days'));
            $date = date('Y-m-d H:i:s');
            $seconds = strtotime($campaignDate) - strtotime($date);

            $days    = floor($seconds / 86400);
            if (!($days < 3 && $days >= 0)) {
                if ($row->social_post_id == null &&  $row->invoice_id == null && $row->is_paused == 0) {
                    if ($row->review == null) {
                        $row->update(['finish' => 0, 'review' => 0]);
                    }
                }
            }
        }
    }
}
