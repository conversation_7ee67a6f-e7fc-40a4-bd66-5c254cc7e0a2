<?php
/**
 * SocialConnectUpdate Command
 *
 * This Laravel Artisan command is responsible for updating social media profile data for all users
 * who have connected their accounts (Twitter, YouTube, Twitch, Facebook, Instagram, TikTok).
 * It fetches the latest profile information, such as followers count, profile picture, and display name,
 * from each respective platform using their APIs and updates the local database records accordingly.
 *
 * Error handling and logging are implemented for each platform to ensure issues are traceable.
 *
 * To run this command manually, use:
 *   php artisan connect:social
 */

namespace App\Console\Commands;

use App\Models\SocialConnect;
use Socialite;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use App\Models\SocialKeys;
use Storage;

class SocialConnectUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'connect:social';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Social Connect';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $socialConnects = SocialConnect::all();
        foreach ($socialConnects as $socialConnect) {
            switch ($socialConnect->media) {
                // case 'twitter':
                //     if ($socialConnect->token && $socialConnect->token_secret) {
                //         $this->updateTwitter($socialConnect);
                //     }
                //     break;
                // case 'youtube':
                //     if ($socialConnect->token) {
                //         $this->updateYouTube($socialConnect);
                //     }
                //     break;
                // case 'twitch':
                //     if ($socialConnect->token && $socialConnect->token_secret) {
                //         $this->updateTwitch($socialConnect);
                //     }
                //     break;
                // case 'facebook':
                //     if ($socialConnect->token && $socialConnect->token_secret) {
                //         $this->updateFacebook($socialConnect);
                //     }
                //     break;
                case 'instagram':
                    if ($socialConnect->token && $socialConnect->token_secret) {
                        $this->updateInstagram($socialConnect);
                    }
                    break;
                // case 'tiktok':
                //     if ($socialConnect->token) {
                //         $this->updateTikTok($socialConnect);
                //     }
                //     break;
            }
        }
        return 0;
    }

    private function updateInstagram($socialConnect)
    {
        try {
            $result = SocialKeys::first();
            $appId = $result->instagram_app_id;
            $secret = $result->instagram_app_secret;
            $redirectUri = config('app.url') . $result->instagram_callback_url;
            $url = 'https://graph.facebook.com/v2.3/me/accounts?access_token=' . $socialConnect->token;
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            $json = json_decode(curl_exec($ch));
            curl_close($ch);
            if (isset($json->data[0]->access_token)) {
                $access_token = $json->data[0]->access_token;
            } else {
                $access_token = $socialConnect->token;
            }
            $socialConnect->update([
                'token' => $access_token
            ]);
            $insta_user_id = $socialConnect->token_secret;
            $url = "https://graph.facebook.com/" . $insta_user_id . "?fields=id,name,username,profile_picture_url,followers_count&access_token=" . @$socialConnect->token;
            $client = new Client();
            $response = $client->request('GET', $url);
            $content = $response->getBody()->getContents();
            $oAuth = json_decode($content);
            $name = (@$oAuth->username) ? @$oAuth->username : @$oAuth->name;
            $followers_count = @$oAuth->followers_count;
            $profileUrl = "https://www.instagram.com/" . @$oAuth->username;
            $picture = @$oAuth->profile_picture_url;
            if (isset($picture)) {
                $fileContents = file_get_contents($picture);
                $filename = str_random(40);
                Storage::disk('public')->put('social_pics/' . $filename . '.jpg', $fileContents);
                $socialConnect->update([
                    'followers' => $followers_count,
                    'name' => $name,
                    'picture' => 'social_pics/' . $filename . '.jpg',
                ]);
            } else {
                $socialConnect->update([
                    'followers' => $followers_count,
                    'name' => $name,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Instagram update failed for SocialConnect ID ' . $socialConnect->id . ': ' . $e->getMessage());
        }
    }

    // private function updateTwitter($socialConnect)
    // {
    //     try {
    //         $user = Socialite::driver('twitter')->userFromTokenAndSecret($socialConnect->token, $socialConnect->token_secret);
    //         $fileContents = file_get_contents(str_replace('http://', 'https://', @$user->getAvatar()));
    //         $filename = str_random(40);
    //         Storage::disk('public')->put('social_pics/' . $filename . '.jpg', $fileContents);
    //         $name = (@$user->nickname) ? @$user->nickname : @$user->name;
    //         $socialConnect->update([
    //             'followers' => @$user['followers_count'],
    //             'picture' => 'social_pics/' . $filename . '.jpg',
    //             'name' => $name,
    //         ]);
    //     } catch (\Exception $e) {
    //         Log::error('Twitter update failed for SocialConnect ID ' . $socialConnect->id . ': ' . $e->getMessage());
    //     }
    // }

    // private function updateYouTube($socialConnect)
    // {
    //     try {
    //         $url = 'https://www.googleapis.com/youtube/v3/channels?part=snippet&id=' . $socialConnect->social_id . '&key=' . env('YOUTUBE_API_KEY');
    //         $ch = curl_init();
    //         curl_setopt($ch, CURLOPT_URL, $url);
    //         curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    //         $channelOBJ = json_decode(curl_exec($ch), true);
    //         curl_close($ch);
    //         if (isset($channelOBJ['items'])) {
    //             $fileContents = file_get_contents($channelOBJ['items'][0]['snippet']['thumbnails']['default']['url']);
    //             $filename = str_random(40);
    //             Storage::disk('public')->put('social_pics/' . $filename . '.jpg', $fileContents);
    //         }
    //         $youtube_subscribers = file_get_contents('https://www.googleapis.com/youtube/v3/channels?part=statistics&id=' . $socialConnect->social_id . '&key=' . env('YOUTUBE_API_KEY'));
    //         $youtube_api_response = json_decode($youtube_subscribers, true);
    //         $followers_count = intval($youtube_api_response['items'][0]['statistics']['subscriberCount']);
    //         $socialConnect->update([
    //             'followers' => $followers_count,
    //         ]);
    //         if (isset($channelOBJ['items'])) {
    //             $socialConnect->update([
    //                 'picture' => 'social_pics/' . $filename . '.jpg',
    //                 'name' => $channelOBJ['items'][0]['snippet']['title'],
    //             ]);
    //         }
    //     } catch (\Exception $e) {
    //         Log::error('YouTube update failed for SocialConnect ID ' . $socialConnect->id . ': ' . $e->getMessage());
    //     }
    // }

    // private function updateTwitch($socialConnect)
    // {
    //     try {
    //         $result = SocialKeys::first();
    //         $appId = $result->twitch_app_id;
    //         $secret = $result->twitch_app_secret;
    //         $ch = curl_init();
    //         curl_setopt($ch, CURLOPT_URL, "https://id.twitch.tv/oauth2/token");
    //         curl_setopt($ch, CURLOPT_POST, 1);
    //         curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    //             'client_id' => $appId,
    //             'client_secret' => $secret,
    //             'grant_type' => 'refresh_token',
    //             'refresh_token' => $socialConnect->social_id
    //         ]));
    //         curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //         $response = curl_exec($ch);
    //         curl_close($ch);
    //         $content = json_decode($response);
    //         $videosApi = 'https://api.twitch.tv/helix/users/follows?to_id=' . $socialConnect->token;
    //         $clientId = config('services.twitch.client_id');
    //         $ch = curl_init();
    //         curl_setopt_array($ch, array(
    //             CURLOPT_HTTPHEADER => array(
    //                 "Accept: application/vnd.twitchtv.v5+json",
    //                 'Client-ID: ' . $clientId,
    //                 "Authorization: Bearer " . $content->access_token
    //             ),
    //             CURLOPT_SSL_VERIFYPEER => false,
    //             CURLOPT_RETURNTRANSFER => true,
    //             CURLOPT_URL => $videosApi
    //         ));
    //         $response = curl_exec($ch);
    //         curl_close($ch);
    //         $data = json_decode($response, true);
    //         $videosApi = 'https://api.twitch.tv/helix/users';
    //         $ch = curl_init();
    //         curl_setopt_array($ch, array(
    //             CURLOPT_HTTPHEADER => array(
    //                 "Accept: application/vnd.twitchtv.v5+json",
    //                 'Client-ID: ' . $appId,
    //                 "Authorization: Bearer " . $content->access_token
    //             ),
    //             CURLOPT_SSL_VERIFYPEER => false,
    //             CURLOPT_RETURNTRANSFER => true,
    //             CURLOPT_URL => $videosApi
    //         ));
    //         $response = curl_exec($ch);
    //         curl_close($ch);
    //         $oAuth = json_decode($response, true);
    //         $username = $oAuth['data'][0]['display_name'];
    //         $picture = $oAuth['data'][0]['profile_image_url'];
    //         $fileContents = file_get_contents($picture);
    //         $filename = str_random(40);
    //         Storage::disk('public')->put('social_pics/' . $filename . '.jpg', $fileContents);
    //         $socialConnect->update([
    //             'picture' => 'social_pics/' . $filename . '.jpg',
    //             'name' => $username,
    //         ]);
    //         if (isset($data['total'])) {
    //             $socialConnect->update([
    //                 'followers' => $data['total'],
    //                 'token_secret' => $content->access_token
    //             ]);
    //         }
    //     } catch (\Exception $e) {
    //         Log::error('Twitch update failed for SocialConnect ID ' . $socialConnect->id . ': ' . $e->getMessage());
    //     }
    // }

    // private function updateFacebook($socialConnect)
    // {
    //     try {
    //         $result = SocialKeys::first();
    //         $appId = $result->facebook_app_id;
    //         $secret = $result->facebook_app_secret;
    //         $redirectUri = config('app.url') . $result->facebook_callback_url;
    //         $ch = curl_init();
    //         curl_setopt($ch, CURLOPT_URL, "https://graph.facebook.com/oauth/access_token");
    //         curl_setopt($ch, CURLOPT_POST, 1);
    //         curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    //             'grant_type' => 'fb_exchange_token',
    //             'client_id' => $appId,
    //             'client_secret' => $secret,
    //             'fb_exchange_token' => $socialConnect->token,
    //         ]));
    //         curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //         $response = curl_exec($ch);
    //         curl_close($ch);
    //         $content = json_decode($response);
    //         $socialConnect->update([
    //             'token' => $content->access_token
    //         ]);
    //         $url = "https://graph.facebook.com/" . $socialConnect->token_secret . "?fields=id,name,picture,followers_count,link&access_token=" . $content->access_token;
    //         $client = new Client();
    //         $response = $client->request('GET', $url);
    //         $content = $response->getBody()->getContents();
    //         $oAuth = json_decode($content);
    //         $name = (@$oAuth->username) ? @$oAuth->username : @$oAuth->name;
    //         $picture = @$oAuth->picture->data->url;
    //         if (isset($picture)) {
    //             $fileContents = file_get_contents($picture);
    //             $filename = str_random(40);
    //             Storage::disk('public')->put('social_pics/' . $filename . '.jpg', $fileContents);
    //             $socialConnect->update([
    //                 'picture' => 'social_pics/' . $filename . '.jpg',
    //                 'name' => $name,
    //             ]);
    //         } else {
    //             $socialConnect->update([
    //                 'name' => $name,
    //             ]);
    //         }
    //         $socialConnect->update([
    //             'followers' => @$oAuth->followers_count
    //         ]);
    //     } catch (\Exception $e) {
    //         Log::error('Facebook update failed for SocialConnect ID ' . $socialConnect->id . ': ' . $e->getMessage());
    //     }
    // }

    // private function updateTikTok($socialConnect)
    // {
    //     try {
    //         $videosApi = "https://open.tiktokapis.com/v2/user/info/?fields=open_id,union_id,avatar_url,follower_count";
    //         $ch = curl_init();
    //         curl_setopt_array($ch, array(
    //             CURLOPT_HTTPHEADER => array(
    //                 "Authorization: Bearer " . $socialConnect->token
    //             ),
    //             CURLOPT_SSL_VERIFYPEER => false,
    //             CURLOPT_RETURNTRANSFER => true,
    //             CURLOPT_URL => $videosApi
    //         ));
    //         $response = curl_exec($ch);
    //         curl_close($ch);
    //         $oAuth = json_decode($response, true);
    //         if (isset($oAuth['data']['user'])) {
    //             $picture = $oAuth['data']['user']['avatar_url'];
    //             $fileContents = file_get_contents($picture);
    //             $filename = str_random(40);
    //             Storage::disk('public')->put('social_pics/' . $filename . '.jpg', $fileContents);
    //             $socialConnect->update([
    //                 'followers' => $oAuth['data']['user']['follower_count'],
    //                 'picture' => 'social_pics/' . $filename . '.jpg'
    //             ]);
    //         }
    //     } catch (\Exception $e) {
    //         Log::error('TikTok update failed for SocialConnect ID ' . $socialConnect->id . ': ' . $e->getMessage());
    //     }
    // }
}
