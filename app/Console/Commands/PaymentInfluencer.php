<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\InfluencerRequestDetail;
use App\Models\User; 
use App\Notifications\paidToInfluencer;
use Carbon\Carbon;
use App\Models\StripeAccount; 
use App\Models\TransferInfluencer; 
 
class PaymentInfluencer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment:influencer';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
            
      $details = InfluencerRequestDetail::where('invoice_id','!=',null)->get(); 
      foreach ($details as $row) { 


        // if($row->refund_reason == null  && $row->social_post_id != null  ){ 
 
        //      // && ( Carbon::now()->format('Y-m-d') == Carbon::parse($row->invoices->created_at)->addDays(1)->format('Y-m-d')  || Carbon::now()->format('Y-m-d') > Carbon::parse($row->invoices->created_at)->addDays(1)->format('Y-m-d') )


        //     $stripe_account = StripeAccount::where('user_id',$row->influencerdetails->user_id)->first();

        //     if($stripe_account!=null){
        //         // \Log::info('stripe connected'); 

        //         \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));

        //         // $admin_tot = ($parentGroupMemberTotal*$admin_comission->percentage)/100;
        //         if(TransferInfluencer::where('influencer_request_detail_id',$row->id)->where('influencer_id',$stripe_account->user_id)->first() == ''){
        //             if(intval($row->total_amount)-intval($row->discount_price) > 0 ){
        //                     try{
        //                     \Log::info('Payment given to influencer for campaign '.$row->name); 
        //                         $transfer = \Stripe\Transfer::create([
        //                             'amount' => ( intval($row->total_amount)-intval($row->discount_price) ) *100   ,
        //                             'currency' => 'EUR',
        //                             'destination' => $stripe_account->stripe_user_id,
        //                             'description' => 'Payment given to influencer for campaign '.$row->name.' at '.env('APP_NAME') 
        //                         ]);  
        //                         \Log::info($transfer); 
        //                         if($transfer){ 
        //                             TransferInfluencer::create(
        //                                 ['influencer_request_detail_id' => $row->id,
        //                                 'influencer_id' => $stripe_account->user_id,
        //                                  'card_token' => $transfer->id,  
        //                                  'payment_status' =>'Completed',  
        //                                  'paid_amount' =>  ( intval($row->total_amount)-intval($row->discount_price) ) *100  ,  
        //                                 'description' =>'Transfer' 
        //                             ]); 
        //                             $user = User::find($stripe_account->user_id);
        //                             $user->notify(new paidToInfluencer($user, $row));
        //                         }
        //                     }catch(\Exception $e){
        //                           \Log::info($e);    
        //                     }
        //                 }
        //         }
        //     }
        // }
      } 

 
        return 0;
    }
}
