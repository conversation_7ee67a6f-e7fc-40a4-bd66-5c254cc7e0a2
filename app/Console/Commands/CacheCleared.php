<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

class CacheCleared extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clear:cache';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all Laravel caches (application, route, config, view)';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Clearing all caches...');
        
        // Clear application cache
        $this->info('Clearing application cache...');
        cache()->flush();
        Artisan::call('cache:clear');
        $this->info('Application cache cleared!');
        
        // Clear route cache
        $this->info('Clearing route cache...');
        Artisan::call('route:clear');
        $this->info('Route cache cleared!');
        
        // Clear configuration cache
        $this->info('Clearing configuration cache...');
        Artisan::call('config:clear');
        $this->info('Configuration cache cleared!');
        
        // Clear view cache
        $this->info('Clearing view cache...');
        Artisan::call('view:clear');
        $this->info('View cache cleared!');
        
        // Clear compiled class files
        $this->info('Clearing compiled class files...');
        Artisan::call('clear-compiled');
        $this->info('Compiled class files cleared!');
        
        // Optimize Composer's class autoloader
        $this->info('Optimizing Composer autoloader...');
        try {
            $process = new Process(['composer', 'dump-autoload', '-o']);
            $process->setTimeout(300); // 5 minutes
            $process->run();
            
            if ($process->isSuccessful()) {
                $this->info('Composer autoloader optimized!');
            } else {
                throw new ProcessFailedException($process);
            }
        } catch (\Exception $e) {
            $this->warn('Could not optimize Composer autoloader: ' . $e->getMessage());
            $this->line('Please run "composer dump-autoload -o" manually.');
        }
        
        $this->info('All caches have been cleared successfully!');
        
        return 0;
    }
}
