<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\InfluencerRequestDetail;
use App\Models\User; 
use App\Notifications\PaymentRefund;
use Carbon\Carbon;
use App\Models\StripeAccount; 
use App\Models\TransferInfluencer; 
use App\Models\AdminGamification;
 
class TrophyCalc extends Command
{
    /**
     * The name and signature of the console command.
     *
     * var string
     */
    protected $signature = 'trophy:calc';

    /**
     * The console command description.
     *
     * var string
     */
    protected $description = 'Trophy Calc';

    /**
     * Create a new command instance.
     *
     * return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * return int
     */
    public function handle()
    {
             
    //     $requests =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
    //                     ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
    //                     ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )     
    //                     ->leftjoin('social_posts',  'social_posts.id', '=', 'influencer_request_details.social_post_id' )  
    //                     ->leftjoin('rating_reviews',  'rating_reviews.influencer_request_accept_id', '=', 'influencer_request_accepts.id' )
    //                     ->leftjoin('complaints',  'complaints.influencer_request_accept_id', '=', 'influencer_request_accepts.id' )  
    //                     ->select('influencer_request_details.*',  'social_posts.like','social_posts.comment','social_posts.view','social_posts.share','social_posts.friend','social_posts.favorite','influencer_request_accepts.request','rating_reviews.rating','complaints.status as complaints_status' )
    //                     ->where('influencer_details.user_id',Auth::id()) 
    //                     ->groupBy('influencer_request_details.compaign_id')
    //                     ->orderBy('influencer_request_details.id','desc')
    //                     ->get();   
    //     $results = AdminGamification::where('select_type','Point-Rules')->first();

    //     $user = User::find(Auth::id());    
    //     $points = 0 ; 
    //     foreach($requests as $request){ 

    //                 // <!-- points_five_star -->
    //                 if(isset($request->rating) && $request->rating == '5.0'   && $request->refund_reason ==null ){
    //                       $points =$points + $results->points_five_star ;   
    //                   }

    //                 // <!-- points_four_star -->
    //                 if(isset($request->rating) && $request->rating == '4.0'    && $request->refund_reason ==null){
    //                     $points =$points + $results->points_four_star ;  
    //                 } 
    //                 // <!-- points_completed_time  points_half_time  --> 
                      
    //                 $created_at = strtotime($request->created_at);  
    //                 $updated_at = strtotime($request->updated_at);    
    //                 $datediff = $updated_at - $created_at; 
    //                 $days =  round($datediff / (60 * 60 * 24)); 
                    
    //                 if( $days < ($request->time)/2  && $request->social_post_id !=null   && $request->refund_reason ==null ){
    //                       $points =$points + $results->points_completed_time ;    
    //                   }    
    //                 elseif( $days <  $request->time  && $request->social_post_id !=null   && $request->refund_reason ==null ) {
    //                     $points =$points + $results->points_half_time ;    
    //                  }                            
    //                 // <!-- quick_response -->
                    
    //                 $created_date = $request->created_at;
    //                 $influencer_request_accepts = App\Models\InfluencerRequestAccept::where('influencer_request_detail_id',$request->id)->where('user_id',Auth::id())->first(); 
    //                 if($influencer_request_accepts != ''){
    //                             $created_at = strtotime($request->created_at);  
    //                             $updated_at = strtotime($influencer_request_accepts->created_at);  
    //                             $datediff = $updated_at - $created_at; 
    //                             $days =  round($datediff / (60 * 60 * 24)); 
                            
    //                         if(isset($influencer_request_accepts) && $days <= 1   && $request->refund_reason ==null ){
    //                               $points =$points + $results->quick_response ; 
    //                           }        


    //                         // <!-- points_deadlines -->
    //                         if(isset($influencer_request_accepts) && $days >2   && $request->refund_reason ==null  ){ 
    //                               $points =$points - $results->points_deadlines ;   
    //                               if($points<0){
    //                                 $points=0;
    //                               }
    //                          }  

    //                   } 
    //                 // <!-- points_one_star -->
    //                 if(isset($request->rating) && $request->rating == '1.0' ){
    //                       $points =$points - $results->points_one_star ;    
    //                       if($points<0){
    //                         $points=0;
    //                       }
                            
    //                   }
    //                 // <!-- points_two_star -->
    //                 if(isset($request->rating) && $request->rating == '2.0' ){
    //                       $points =$points - $results->points_two_star ;      
    //                       if($points<0){
    //                         $points=0;
    //                       }
                            
    //                   }

                    
    //                 // <!-- points_disputes -->
    //                  if(isset($request->complaints_status) && $request->complaints_status == 'Confirmed' ){
    //                       $points =$points - $results->points_disputes ; 
    //                       if($points<0){
    //                         $points=0;
    //                       }      
    //                   } 
                    
    //         }       
                

    //     // <!-- repeat_bookings -->
         
    //     $influencer_request_details = InfluencerRequestDetail::where('influencer_detail_id',$requests[0]->influencer_detail_id)->where('user_id',$requests[0]->user_id)->where('social_post_id', '!=',null)->count();  
 
        
    //     if(isset($influencer_request_details) && $influencer_request_details > 0 ) {
    //           $points =$points + $results->repeat_bookings ;      
    //       }

        

    //     // <!-- daily_login -->  
        
    //     //      $daily_count = App\Models\UserDailyLogin::where('user_id',Auth::id())->where(DB::raw("(STR_TO_DATE(created_at,'%Y-%m-%d'))"), date('Y-m-d'))->count();
    //     //     $user = App\Models\User::where('id', Auth::id())->first(); 
    //     //     $created_at = strtotime($user->created_at);  
    //     //     $updated_at = strtotime(date('Y-m-d'));  
    //     //     $datediff = $updated_at - $created_at; 
    //     //      $days =  (round($datediff / (60 * 60 * 24))+1);  
        
    //     // if( intval($days) == $daily_count ) {
    //     //       $points =$points + $results->daily_login ;   
    //     //   }

         

    //     // <!-- successfull_campaign -->
         
    //   //       $influencer_request_details = App\Models\InfluencerRequestDetail::where('influencer_detail_id',$requests[0]->influencer_detail_id)->where('user_id',$requests[0]->user_id)->where('finish',1)->count();  
        
    //   //   if(isset($influencer_request_details) && ($influencer_request_details%3) == 0 ) {
    //   //     $points =$points + $results->successfull_campaign ;      
    //   // }  


    //   // $trophy = '';
    //   // if($points < 10000){
    //   //   $trophy = 'Platinum';
    //   // } 
    //   // if($points < 2000){
    //   //   $trophy = 'Gold';
    //   // }
    //   // if($points < 900){
    //   //   $trophy = 'Silver';
    //   // }

    //   // $user->update(['trophy' => $trophy]);


    //     $pricing = AdminGamification::where('select_type','Pricing & Rank')->get(); 
    //      foreach($pricing as $price){
    //           $trophy = 'Bronze';
    //           if($points > ($price->requirement-1)){
    //              $trophy = $price->type ;
    //           }  
    //           $user->update(['trophy' => $trophy]);
    //      }  

    }
}
