<?php

namespace App\Console\Commands;

use App\Jobs\ChargeInfluencers;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CommissionCharge extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'commission:charge';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To Charge Comission for Pending Campaigns and mark them complete as payment ready for the payout';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ChargeInfluencers::dispatch();
    }
}
