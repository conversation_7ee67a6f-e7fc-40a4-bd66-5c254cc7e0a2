<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Tests\Feature\StripePaymentTest;

class DebugStripePayment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:stripe-payment {campaign_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug Stripe payment calculations for a campaign';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $campaignId = $this->argument('campaign_id');
        
        $this->info('Debugging Stripe payment calculations' . ($campaignId ? " for campaign $campaignId" : ''));
        $this->newLine();
        
        $test = new StripePaymentTest();
        $test->debugPaymentCalculations($campaignId);
        
        return 0;
    }
}