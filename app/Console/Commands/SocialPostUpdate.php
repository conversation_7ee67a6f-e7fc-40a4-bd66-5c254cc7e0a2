<?php

namespace App\Console\Commands;

use App\Models\SocialConnect;
use App\Models\SocialPost;
use App\Models\InfluencerRequestDetail;
use Socialite;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use App\Models\SocialKeys;
use Storage;
use App\Helpers\Instagram\InsightsForTypePost;
use App\Helpers\Instagram\InsightsForTypeStory;

class SocialPostUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'post:social';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update influencers social posts from cron';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $influencerRequestDetails = InfluencerRequestDetail::get();

        foreach ($influencerRequestDetails as $influencerRequestDetail) {
            // Always check for null before accessing nested properties
            if (!isset($influencerRequestDetail->influencerdetails) || !isset($influencerRequestDetail->influencerdetails->user_id)) {
                Log::warning('Influencer details missing', [
                    'InfluencerRequestDetail_ID' => $influencerRequestDetail->id,
                    'row_data' => $influencerRequestDetail->getAttributes(),
                    'file' => __FILE__,
                    'class' => __CLASS__,
                    'function' => __FUNCTION__,
                    'line' => __LINE__
                ]);
                continue;
            }

            $socialConnects = SocialConnect::where('user_id', $influencerRequestDetail->influencerdetails->user_id)->get();

            foreach ($socialConnects as $socialConnect) {
                // TODO only instagram is active now
                if ($socialConnect->media == 'instagram') {
                    $this->importInstagramPostsAndInsights($socialConnect, $influencerRequestDetail);
                }

                // if ($socialConnect->media == 'twitter') {
                //     $this->processTwitter($influencerRequestDetail, $socialConnect);
                // } elseif ($socialConnect->media == 'youtube') {
                //     $this->processYouTube($influencerRequestDetail, $socialConnect);
                // } elseif ($socialConnect->media == 'facebook') {
                //     $this->processFacebook($influencerRequestDetail, $socialConnect);
                // } elseif ($socialConnect->media == 'tiktok') {
                //     $this->processTikTok($influencerRequestDetail, $socialConnect);
                // } elseif ($socialConnect->media == 'twitch') {
                //     $this->processTwitch($influencerRequestDetail, $socialConnect);
                // } elseif ($socialConnect->media == 'instagram') {
                //     $this->processInstagram($influencerRequestDetail, $socialConnect);
                // }
            }
        }
    }

    private function importInstagramPostsAndInsights($socialConnect, $influencerRequestDetail) {
        $postImporter = new InsightsForTypePost($socialConnect, $influencerRequestDetail);
        $postImporter->importSocialMediaPost();

        $storyImporter = new InsightsForTypeStory($socialConnect, $influencerRequestDetail);
        $storyImporter->importSocialMediaStory();
    }

    // private function processInstagramStories($influencerRequestDetail, $socialConnect, $insta_user_id, $accessToken) {
    //     $url1 = 'https://graph.facebook.com/v18.0/' . $insta_user_id . '/stories?fields=id,caption,media_type,thumbnail_url,media_url,permalink,timestamp&access_token=' . $accessToken;
    //     $ch1 = curl_init($url1);
    //     curl_setopt($ch1, CURLOPT_RETURNTRANSFER, true);
    //     $ch1_result = curl_exec($ch1);
    //     if ($ch1_result === false) {
    //         Log::error('cURL error in processInstagramStories', [
    //             'error' => curl_error($ch1),
    //             'errno' => curl_errno($ch1),
    //             'url' => $url1,
    //             'user_id' => $socialConnect->user_id,
    //             'insta_user_id' => $insta_user_id,
    //             'file' => __FILE__,
    //             'line' => __LINE__
    //         ]);

    //         return;
    //     }

    //     $data1 = json_decode($ch1_result);
    //     curl_close($ch1);

    //     if (isset($data1->data) && is_array($data1->data)) {
    //         if (count($data1->data) > 0) {
    //             Log::info('Instagram Stories data', [
    //                 'user_id' => $socialConnect->user_id,
    //                 'insta_user_id' => $insta_user_id,
    //                 'stories_data' => $data1->data
    //             ]);
    //         }

    //         foreach ($data1->data as $key => $value) {
    //             $social = SocialPost::where('user_id', $socialConnect->user_id)
    //                 ->where('media', 'instagram')
    //                 ->where('post_id', @$value->id)
    //                 ->first();
    //             // As per the official facebook api documentation, replies is not available in europe
    //             $url = 'https://graph.facebook.com/v18.0/' .
    //                 $value->id .
    //                 '/insights?metric=views,reach,replies,shares,total_interactions&access_token=' .
    //                 $accessToken;
    //             $ch = curl_init($url);
    //             curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    //             if ($social) {
    //                 Log::info('Existing SocialPost found', [
    //                     'user_id' => $socialConnect->user_id,
    //                     'post_id' => @$value->id,
    //                     'social_data' => $social->toArray()
    //                 ]);
    //             } else {
    //                 Log::info('No existing SocialPost found', [
    //                     'user_id' => $socialConnect->user_id,
    //                     'post_id' => @$value->id
    //                 ]);
    //             }

    //             $ch_result = curl_exec($ch);
    //             if ($ch_result === false) {
    //                 Log::error('cURL error in processInstagramStories (insights)', [
    //                     'error' => curl_error($ch),
    //                     'errno' => curl_errno($ch),
    //                     'url' => $url,
    //                     'user_id' => $socialConnect->user_id,
    //                     'insta_user_id' => $insta_user_id,
    //                     'file' => __FILE__,
    //                     'line' => __LINE__
    //                 ]);
    //                 curl_close($ch);
    //                 continue;
    //             }

    //             $insights = json_decode($ch_result);

    //             Log::debug('Instagram Story Insights raw data', [
    //                 'user_id' => $socialConnect->user_id,
    //                 'insta_user_id' => $insta_user_id,
    //                 'story_id' => @$value->id,
    //                 'insights_raw' => $insights
    //             ]);

    //             curl_close($ch);

    //             $file = '';
    //             $media = '';
    //             $likes = 0;
    //             $shares = 0;
    //             $comments = 0;
    //             $views = 0;

    //             if (isset($insights->data)) {
    //                 Log::info('Instagram Story Insights', [
    //                     'user_id' => $socialConnect->user_id,
    //                     'insta_user_id' => $insta_user_id,
    //                     'story_id' => @$value->id,
    //                     'insights_data' => $insights->data
    //                 ]);

    //                 foreach ($insights->data as $data_count) {
    //                     if (isset($data_count->name) && $data_count->name == 'reach') {
    //                         $comments = $data_count->values[0]->value;
    //                     }

    //                     if (isset($data_count->name) && $data_count->name == 'views') {
    //                         $views = $data_count->values[0]->value;
    //                     }
                        
    //                     if (isset($data_count->name) && $data_count->name == 'total_interactions') {
    //                         $likes = $data_count->values[0]->value;
    //                     }

    //                     if (isset($data_count->name) && $data_count->name == 'shares') {
    //                         $shares = $data_count->values[0]->value;
    //                     }
    //                 }
    //             }

    //             if (
    //                 date('Y-m-d', strtotime($value->timestamp)) >= date('Y-m-d', strtotime($influencerRequestDetail->created_at)) &&
    //                 date('Y-m-d', strtotime($value->timestamp)) <= date('Y-m-d')
    //             ) {
    //                 Log::info('All the timestamp check was successful', [
    //                     'story_timestamp' => $value->timestamp,
    //                     'row_created_at' => $influencerRequestDetail->created_at,
    //                     'current_date' => date('Y-m-d')
    //                 ]);

    //                 if (isset($value->media_url)) {
    //                     $fileContents = file_get_contents($value->media_url);
    //                     $filename = @$value->id . '_instagram';
    //                     Storage::disk('public')->put('social_pics/' . $filename . ($value->media_type == 'IMAGE' || $value->media_type == 'CAROUSEL_ALBUM' ? '.jpg' : '.mp4'),
    //                         $fileContents
    //                     );
    //                     $file = 'social_pics/' . $filename . ($value->media_type == 'IMAGE' || $value->media_type == 'CAROUSEL_ALBUM' ? '.jpg' : '.mp4');
    //                     if ($value->media_type == 'IMAGE' || $value->media_type == 'CAROUSEL_ALBUM') {
    //                         $media = 'photo';
    //                     } else {
    //                         $media = 'video';
    //                     }
    //                 }

    //                 $permalink = (@$value->permalink != '') ? @$value->permalink : 'https://www.instagram.com/stories/' . $socialConnect->name;
    //                 if (isset($social)) {
    //                     $social->update([
    //                         'influencer_request_accept_id' => 'story',
    //                         'text' => @$value->caption,
    //                         'link' => (isset($file)) ? $file : "",
    //                         'type' => $media,
    //                         'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
    //                         'thumbnail' => $permalink,
    //                         'like' => max($likes, $social->like),
    //                         'view' => max($views, $social->view),
    //                         'share' => max($shares, $social->share),
    //                         'comment' => max($comments, $social->comment),
    //                     ]);
    //                 } else {
    //                     SocialPost::create([
    //                         'influencer_request_accept_id' => 'story',
    //                         'user_id' => $socialConnect->user_id,
    //                         'media' => 'instagram',
    //                         'post_id' => @$value->id,
    //                         'text' => @$value->caption,
    //                         'link' => (isset($file)) ? $file : "",
    //                         'type' => $media,
    //                         'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
    //                         'thumbnail' => $permalink,
    //                         'like' => $likes,
    //                         'view' => $views,
    //                         'share' => $shares,
    //                         'comment' => $comments,
    //                     ]);
    //                 }
    //             } else {
    //                 Log::info('Timestamp check was unsuccessful', [
    //                     'story_timestamp' => $value->timestamp,
    //                     'row_created_at' => $influencerRequestDetail->created_at,
    //                     'current_date' => date('Y-m-d')
    //                 ]);
    //             }
    //         }
    //     } else {
    //         Log::warning('No Instagram Stories data returned and no cURL error', [
    //             'user_id' => $socialConnect->user_id,
    //             'insta_user_id' => $insta_user_id,
    //             'url' => $url1,
    //             'response' => $ch1_result,
    //             'file' => __FILE__,
    //             'line' => __LINE__
    //         ]);
    //     }
    // }

    // private function processInstagramPosts($influencerRequestDetail, $socialConnect, $insta_user_id, $accessToken) {
    //     $url = 'https://graph.facebook.com/v18.0/' . $insta_user_id . '/media?fields=id,caption,media_type,thumbnail_url,media_url,permalink,timestamp&access_token=' . $accessToken;
    //     $ch = curl_init($url);
    //     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //     $data = json_decode(curl_exec($ch));
    //     curl_close($ch);

    //     if (isset($data->data)) {
    //         foreach ($data->data as $key => $value) {
    //             $mode = '';
    //             if (str_contains($value->permalink, '/reel/')) {
    //                 $mode = 'reel';
    //             }

    //             $social = SocialPost::where('user_id', $socialConnect->user_id)
    //                 ->where('media', 'instagram')
    //                 ->where('post_id', @$value->id)
    //                 ->first();

    //             $url = 'https://graph.facebook.com/v18.0/' . $value->id . '/insights?metric=likes,comments,shares,reach,views&access_token=' . $accessToken;
    //             $ch = curl_init($url);
    //             curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //             $insights = json_decode(curl_exec($ch));
    //             curl_close($ch);

    //             $file = '';
    //             $media = '';

    //             $likes = 0;
    //             $shares = 0;
    //             $comments = 0;
    //             $views = 0;

    //             if (isset($insights->data)) {
    //                 foreach ($insights->data as $data_count) {
    //                     if ($data_count->name == 'reach') {
    //                         $shares = $data_count->values[0]->value;
    //                     }

    //                     if ($data_count->name == 'views') {
    //                         $views = $data_count->values[0]->value;
    //                     }

    //                     if ($data_count->name == 'comments') {
    //                         $comments = $data_count->values[0]->value;
    //                     }

    //                     if ($data_count->name == 'likes') {
    //                         $likes = $data_count->values[0]->value;
    //                     }
    //                 }
    //             }

    //             if (
    //                 date('Y-m-d', strtotime($value->timestamp)) >= date('Y-m-d', strtotime($influencerRequestDetail->created_at)) &&
    //                 date('Y-m-d', strtotime($value->timestamp)) <= date('Y-m-d')
    //             ) {
    //                 if (isset($value->media_url)) {
    //                     $fileContents = file_get_contents($value->media_url);
    //                     $filename = @$value->id . '_instagram';
    //                     Storage::disk('public')->put('social_pics/' . $filename . ($value->media_type == 'IMAGE' || $value->media_type == 'CAROUSEL_ALBUM' ? '.jpg' : '.mp4'),
    //                         $fileContents
    //                     );
    //                     $file = 'social_pics/' . $filename . ($value->media_type == 'IMAGE' || $value->media_type == 'CAROUSEL_ALBUM' ? '.jpg' : '.mp4');

    //                     if ($value->media_type == 'IMAGE' || $value->media_type == 'CAROUSEL_ALBUM') {
    //                         $media = 'photo';
    //                     } else {
    //                         $media = 'video';
    //                     }
    //                 }

    //                 if (isset($social)) {
    //                     // Update only when the existing like, view, share and comment has lower
    //                     // value than we are updating right now. Sometimes the post is deleted
    //                     // and then this code is run, replacing all the existing insight
    //                     // for the post.
    //                     $social->update([
    //                         'influencer_request_accept_id' => $mode,
    //                         'text' => @$value->caption,
    //                         'link' => (isset($file)) ? $file : "",
    //                         'type' => $media,
    //                         'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
    //                         'thumbnail' => @$value->permalink,
    //                         'like' => max($likes, $social->like),
    //                         'view' => max($views, $social->view),
    //                         'share' => max($shares, $social->share),
    //                         'comment' => max($comments, $social->comment),
    //                     ]);
    //                 } else {
    //                     SocialPost::create([
    //                         'influencer_request_accept_id' => $mode,
    //                         'user_id' => $socialConnect->user_id,
    //                         'media' => 'instagram',
    //                         'post_id' => @$value->id,
    //                         'text' => @$value->caption,
    //                         'link' => (isset($file)) ? $file : "",
    //                         'type' => $media,
    //                         'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
    //                         'thumbnail' => @$value->permalink,
    //                         'like' => $likes,
    //                         'view' => $views,
    //                         'share' => $shares,
    //                         'comment' => $comments,
    //                     ]);
    //                 }
    //             }
    //         }
    //     }
    // }

    // private function processInstagramLivestreams($influencerRequestDetail, $socialConnect, $insta_user_id, $accessToken) {
    //     $url = 'https://graph.facebook.com/v18.0/' . $insta_user_id . '/live_media?fields=id,media_type,media_product_type,media_url,permalink,owner,username,comments&access_token=' . $accessToken;
    //     $ch = curl_init($url);
    //     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //     $data1 = json_decode(curl_exec($ch));
    //     curl_close($ch);

    //     if (isset($data1->data)) {
    //         foreach ($data1->data as $key => $value) {
    //             $mode = 'livestream';
    //             $social = SocialPost::where('user_id', $socialConnect->user_id)
    //                 ->where('media', 'instagram')
    //                 ->where('post_id', @$value->id)
    //                 ->first();
    //             $file = '';
    //             $media = '';

    //             $likes = 0;
    //             $shares = 0;
    //             $views = 0;
    //             $comments = isset($value->comments) ? count($value->comments->data) : 0;

    //             $url = "https://graph.facebook.com/" . $insta_user_id . "?fields=id,name,username,profile_picture_url,followers_count&access_token=" . $accessToken;
    //             $client = new Client();
    //             $response = $client->request('GET', $url);
    //             $content = $response->getBody()->getContents();
    //             $oAuth = json_decode($content);
    //             $name = (@$oAuth->username) ? @$oAuth->username : @$oAuth->name;

    //             $file = 'https://www.instagram.com/' . $name . '/live/';
    //             $value->timestamp = date('Y-m-d');
    //             $media = 'video';

    //             if (isset($social)) {
    //                 $social->update([
    //                     'influencer_request_accept_id' => $mode,
    //                     'text' => @$value->caption,
    //                     'link' => (isset($file)) ? $file : "",
    //                     'type' => $media,
    //                     'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
    //                     'thumbnail' => @$value->permalink,
    //                     'like' => max($likes, $social->like),
    //                     'view' => max($views, $social->view),
    //                     'share' => max($shares, $social->share),
    //                     'comment' => max($comments, $social->comment),
    //                 ]);
    //             } else {
    //                 SocialPost::create([
    //                     'influencer_request_accept_id' => $mode,
    //                     'user_id' => $socialConnect->user_id,
    //                     'media' => 'instagram',
    //                     'post_id' => @$value->id,
    //                     'text' => @$value->caption,
    //                     'link' => (isset($file)) ? $file : "",
    //                     'type' => $media,
    //                     'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
    //                     'thumbnail' => @$value->permalink,
    //                     'like' => $likes,
    //                     'view' => $views,
    //                     'share' => $shares,
    //                     'comment' => $comments,
    //                 ]);
    //             }
    //         }
    //     }
    // }

    // private function processTwitter($influencerRequestDetail, $socialConnect)
    // {
    //     if (
    //         $socialConnect->token != null &&
    //         $socialConnect->token_secret != null &&
    //         $socialConnect->media == $influencerRequestDetail->media &&
    //         $socialConnect->media == 'twitter'
    //     ) {
    //         try {
    //             $socialKeys = SocialKeys::first();
    //             $accessToken = $socialConnect->token;
    //             $tw_user_id = $socialConnect->social_id;
    //             $tw_username = $socialConnect->name;
    //             $redirect = config('app.url') . $socialKeys->twitter_callback_url;

    //             // Get Bearer token
    //             $ch = curl_init();
    //             curl_setopt($ch, CURLOPT_URL, "https://api.twitter.com/oauth2/token");
    //             curl_setopt($ch, CURLOPT_POST, 1);
    //             curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    //                 'client_id' => $socialKeys->twitter_app_id,
    //                 'client_secret' => $socialKeys->twitter_app_secret,
    //                 'grant_type' => 'client_credentials'
    //             ]));
    //             curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //             $response = curl_exec($ch);
    //             curl_close($ch);
    //             $content = json_decode($response);

    //             // Get user timeline
    //             $url = 'https://api.twitter.com/1.1/statuses/user_timeline.json?screen_name=' . $tw_username . '&tweet_mode=extended';
    //             $ch = curl_init();
    //             curl_setopt_array($ch, [
    //                 CURLOPT_HTTPHEADER => [
    //                     "Authorization: Bearer " . $content->access_token
    //                 ],
    //                 CURLOPT_SSL_VERIFYPEER => false,
    //                 CURLOPT_RETURNTRANSFER => true,
    //                 CURLOPT_URL => $url
    //             ]);
    //             $response = curl_exec($ch);
    //             curl_close($ch);
    //             $data_twitter = json_decode($response);

    //             foreach ($data_twitter as $data_row) {
    //                 $social = SocialPost::where('user_id', $socialConnect->user_id)
    //                     ->where('media', 'twitter')
    //                     ->where('post_id', $data_row->id)
    //                     ->first();

    //                 if (
    //                     date('Y-m-d', strtotime($data_row->created_at)) >= date('Y-m-d', strtotime($influencerRequestDetail->created_at)) &&
    //                     date('Y-m-d', strtotime($data_row->created_at)) <= date('Y-m-d')
    //                 ) {
    //                     // Get video views if available
    //                     $url = 'https://api.twitter.com/1.1/videos/tweet/config/' . $data_row->id . '.json';
    //                     $ch = curl_init();
    //                     curl_setopt_array($ch, [
    //                         CURLOPT_HTTPHEADER => [
    //                             "Authorization: Bearer " . $content->access_token
    //                         ],
    //                         CURLOPT_SSL_VERIFYPEER => false,
    //                         CURLOPT_RETURNTRANSFER => true,
    //                         CURLOPT_URL => $url
    //                     ]);
    //                     $response = curl_exec($ch);
    //                     curl_close($ch);
    //                     $data_twitter1 = json_decode($response);
    //                     $views = isset($data_twitter1->track->viewCount) ? $data_twitter1->track->viewCount : '';

    //                     $file = '';
    //                     $type = '';
    //                     if (isset($data_row->extended_entities->media[0]->media_url)) {
    //                         $link = '';
    //                         if ($data_row->extended_entities->media[0]->type == 'video') {
    //                             $type = 'video';
    //                             $link = $data_row->extended_entities->media[0]->video_info->variants[1]->url;
    //                         } elseif (
    //                             $data_row->extended_entities->media[0]->type == 'photo' ||
    //                             $data_row->extended_entities->media[0]->type == 'animated_gif'
    //                         ) {
    //                             $type = 'photo';
    //                             $link = $data_row->extended_entities->media[0]->media_url;
    //                         }
    //                         $fileContents = file_get_contents($link);
    //                         $filename = $data_row->id . '_twitter';
    //                         Storage::disk('public')->put('social_pics/' . $filename . ($data_row->extended_entities->media[0]->type == 'photo' ? '.jpg' : '.mp4'),
    //                             $fileContents
    //                         );
    //                         $file = 'social_pics/' . $filename . ($data_row->extended_entities->media[0]->type == 'photo' ? '.jpg' : '.mp4');
    //                     } else {
    //                         $file = 'https://twitter.com/' . $socialConnect->name . '/status/' . $data_row->id;
    //                     }

    //                     if (isset($social)) {
    //                         $social->update([
    //                             'text' => $data_row->full_text,
    //                             'link' => $file,
    //                             'type' => $type,
    //                             'published_at' => date('Y-m-d H:i:s', strtotime($data_row->created_at)),
    //                             'thumbnail' => 'https://twitter.com/' . $socialConnect->name . '/status/' . $data_row->id,
    //                             // 'like' => $data_row->user->followers_count,
    //                             // 'friend' => $data_row->user->friends_count,
    //                             'like' => $data_row->favorite_count,
    //                             'comment' => $data_row->retweet_count,
    //                             'view' => ($views != '') ? $views : $social->view,
    //                         ]);
    //                     } else {
    //                         SocialPost::create([
    //                             'user_id' => $socialConnect->user_id,
    //                             'media' => 'twitter',
    //                             'post_id' => $data_row->id,
    //                             'text' => $data_row->full_text,
    //                             'link' => $file,
    //                             'type' => $type,
    //                             'published_at' => date('Y-m-d H:i:s', strtotime($data_row->created_at)),
    //                             'thumbnail' => 'https://twitter.com/' . $socialConnect->name . '/status/' . $data_row->id,
    //                             // 'like' => $data_row->user->followers_count,
    //                             // 'friend' => $data_row->user->friends_count,
    //                             'like' => $data_row->favorite_count,
    //                             'comment' => $data_row->retweet_count,
    //                             'view' => $views,
    //                         ]);
    //                     }
    //                 }
    //             }
    //         } catch (\Exception $e) {
    //             Log::error('Twitter API Exception: ' . $e->getMessage(), ['exception' => $e]);
    //         }
    //     }
    // }

    // private function processYouTube($influencerRequestDetail, $socialConnect)
    // {
    //     if (
    //         $socialConnect->token != null &&
    //         $socialConnect->media == $influencerRequestDetail->media &&
    //         $socialConnect->media == 'youtube'
    //     ) {
    //         $result = SocialKeys::first();
    //         try {
    //             Log::info('Youtube call');
    //             $youtube_subscribers = @file_get_contents('https://www.googleapis.com/youtube/v3/channels?part=statistics&id=' . $socialConnect->social_id . '&key=' . env('YOUTUBE_API_KEY'));
    //             $youtube_api_response = json_decode($youtube_subscribers, true);

    //             Log::info('Youtube call');
    //             $youtube_subscribers1 = @file_get_contents('https://www.googleapis.com/youtube/v3/search?order=date&part=snippet&channelId=' . $youtube_api_response['items'][0]['id'] . '&key=' . env('YOUTUBE_API_KEY'));
    //             $data_youtube = json_decode($youtube_subscribers1, true);

    //             Log::info('Youtube call');
    //             $youtube_subscribers3 = file_get_contents('https://yt.lemnoslife.com/channels?part=shorts&id=' . $youtube_api_response['items'][0]['id'] . '&key=' . env('YOUTUBE_API_KEY'));
    //             $data_youtube3 = json_decode($youtube_subscribers3, true);

    //             $mode = '';
    //             $count = 1;
    //             if ($data_youtube != '') {
    //                 foreach ($data_youtube['items'] as $data_row) {
    //                     if ($count < count($data_youtube['items']) && isset($data_row['id']['videoId'])) {
    //                         $mode = '';
    //                         foreach ($data_youtube3['items'][0]['shorts'] as $data_row3) {
    //                             if (isset($data_row3['videoId'])) {
    //                                 if ($data_row['id']['videoId'] == $data_row3['videoId']) {
    //                                     $mode = 'shorts';
    //                                 }
    //                             }
    //                         }
    //                         Log::info('Youtube call');
    //                         $youtube_subscribers4 = file_get_contents('https://www.googleapis.com/youtube/v3/videos?part=liveStreamingDetails&id=' . $data_row['id']['videoId'] . '&key=' . env('YOUTUBE_API_KEY'));
    //                         $data_youtube4 = json_decode($youtube_subscribers4, true);

    //                         if (isset($data_youtube4['items'][0]['liveStreamingDetails'])) {
    //                             $mode = 'livestream';
    //                         }
    //                         Log::info('Youtube call');
    //                         $youtube_subscribers2 = file_get_contents('https://www.googleapis.com/youtube/v3/videos?part=statistics&id=' . $data_row['id']['videoId'] . '&key=' . env('YOUTUBE_API_KEY'));
    //                         $data_youtube2 = json_decode($youtube_subscribers2, true);

    //                         $social = SocialPost::where('user_id', $socialConnect->user_id)
    //                             ->where('media', 'youtube')
    //                             ->where('post_id', $data_row["etag"])
    //                             ->first();
    //                         $file = '';

    //                         if (
    //                             date('Y-m-d', strtotime($data_row["snippet"]["publishedAt"])) >= date('Y-m-d', strtotime($influencerRequestDetail->created_at)) &&
    //                             date('Y-m-d', strtotime($data_row["snippet"]["publishedAt"])) <= date('Y-m-d')
    //                         ) {
    //                             if (isset($social)) {
    //                                 $social->update([
    //                                     'influencer_request_accept_id' => $mode,
    //                                     'text' => $data_row['snippet']['title'],
    //                                     'link' => 'https://www.youtube.com/watch?v=' . $data_row['id']['videoId'],
    //                                     'type' => 'video',
    //                                     'published_at' => date('Y-m-d H:i:s', strtotime($data_row["snippet"]["publishedAt"])),
    //                                     'like' => (isset($data_youtube2['items'][0]['statistics']['likeCount']) && $data_youtube2['items'][0]['statistics']['likeCount'] > 0) ? $data_youtube2['items'][0]['statistics']['likeCount'] : 0,
    //                                     'view' => (isset($data_youtube2['items'][0]['statistics']['viewCount']) && $data_youtube2['items'][0]['statistics']['viewCount'] > 0) ? $data_youtube2['items'][0]['statistics']['viewCount'] : 0,
    //                                     // 'favorite' => (isset($data_youtube2['items'][0]['statistics']['favoriteCount']) && $data_youtube2['items'][0]['statistics']['favoriteCount']>0)?$data_youtube2['items'][0]['statistics']['favoriteCount']:0 ,
    //                                     'comment' => (isset($data_youtube2['items'][0]['statistics']['commentCount']) && $data_youtube2['items'][0]['statistics']['commentCount'] > 0) ? $data_youtube2['items'][0]['statistics']['commentCount'] : 0,
    //                                 ]);
    //                             } else {
    //                                 SocialPost::create([
    //                                     'influencer_request_accept_id' => $mode,
    //                                     'user_id' => $socialConnect->user_id,
    //                                     'media' => 'youtube',
    //                                     'post_id' => $data_row["etag"],
    //                                     'text' => $data_row['snippet']['title'],
    //                                     'link' => 'https://www.youtube.com/watch?v=' . $data_row['id']['videoId'],
    //                                     'type' => 'video',
    //                                     'published_at' => date('Y-m-d H:i:s', strtotime($data_row["snippet"]["publishedAt"])),
    //                                     'like' => (isset($data_youtube2['items'][0]['statistics']['likeCount']) && $data_youtube2['items'][0]['statistics']['likeCount'] > 0) ? $data_youtube2['items'][0]['statistics']['likeCount'] : 0,
    //                                     'view' => (isset($data_youtube2['items'][0]['statistics']['viewCount']) && $data_youtube2['items'][0]['statistics']['viewCount'] > 0) ? $data_youtube2['items'][0]['statistics']['viewCount'] : 0,
    //                                     // 'favorite' => (isset($data_youtube2['items'][0]['statistics']['favoriteCount']) && $data_youtube2['items'][0]['statistics']['favoriteCount']>0)?$data_youtube2['items'][0]['statistics']['favoriteCount']:0 ,
    //                                     'comment' => (isset($data_youtube2['items'][0]['statistics']['commentCount']) && $data_youtube2['items'][0]['statistics']['commentCount'] > 0) ? $data_youtube2['items'][0]['statistics']['commentCount'] : 0,
    //                                 ]);
    //                             }
    //                         }
    //                     }

    //                     // community polls
    //                     $youtube_subscribers5 = file_get_contents('https://yt.lemnoslife.com/channels?part=community&id=' . $youtube_api_response['items'][0]['id']);
    //                     $data_youtube5 = json_decode($youtube_subscribers5, true);

    //                     foreach ($data_youtube5['items'] as $data_row) {
    //                         if (isset($data_row['id'])) {
    //                             $mode = 'polls';

    //                             $social = SocialPost::where('user_id', $socialConnect->user_id)
    //                                 ->where('media', 'youtube')
    //                                 ->where('post_id', $data_row["id"])
    //                                 ->first();
    //                             $file = '';

    //                             if (isset($social)) {
    //                                 $social->update([
    //                                     'influencer_request_accept_id' => $mode,
    //                                     'text' => $data_row['community'][0]['contentText'][0]['text'],
    //                                     'link' => 'https://www.youtube.com/' . $socialConnect->token_secret . '/community',
    //                                     'type' => '',
    //                                     'published_at' => date('Y-m-d H:i:s'),
    //                                     'like' => (isset($data_row['community'][0]['likes']) && $data_row['community'][0]['likes'] > 0) ? $data_row['community'][0]['likes'] : 0,
    //                                     'comment' => (isset($data_row['community'][0]['commentsCount']) && $data_row['community'][0]['commentsCount'] > 0) ? $data_row['community'][0]['commentsCount'] : 0,
    //                                 ]);
    //                             } else {
    //                                 SocialPost::create([
    //                                     'influencer_request_accept_id' => $mode,
    //                                     'user_id' => $socialConnect->user_id,
    //                                     'media' => 'youtube',
    //                                     'post_id' => $data_row['id'],
    //                                     'text' => $data_row['community'][0]['contentText'][0]['text'],
    //                                     'link' => 'https://www.youtube.com/' . $socialConnect->token_secret . '/community',
    //                                     'type' => '',
    //                                     'published_at' => date('Y-m-d H:i:s'),
    //                                     'like' => (isset($data_row['community'][0]['likes']) && $data_row['community'][0]['likes'] > 0) ? $data_row['community'][0]['likes'] : 0,
    //                                     'comment' => (isset($data_row['community'][0]['commentsCount']) && $data_row['community'][0]['commentsCount'] > 0) ? $data_row['community'][0]['commentsCount'] : 0,
    //                                 ]);
    //                             }
    //                         }
    //                     }
    //                 }
    //             }
    //         } catch (\Exception $e) {
    //             Log::error('YouTube API Exception: ' . $e->getMessage(), ['exception' => $e]);
    //         }
    //     }
    // }

    // private function processFacebook($influencerRequestDetail, $socialConnect)
    // {
    //     if (
    //         $socialConnect->token != null &&
    //         $socialConnect->token_secret != null &&
    //         $socialConnect->media == $influencerRequestDetail->media &&
    //         $socialConnect->media == 'facebook'
    //     ) {
    //         $result = SocialKeys::first();
    //         $appId = $result->facebook_app_id;
    //         $secret = $result->facebook_app_secret;
    //         $redirectUri = config('app.url') . $result->facebook_callback_url;

    //         $url = 'https://graph.facebook.com/' . $socialConnect->token_secret . '/posts?access_token=' . $socialConnect->social_id . '&fields=message,created_time,full_picture,from,place,attachments{media_type,media,type,title,description,target,subattachments}';
    //         $ch = curl_init($url);
    //         curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //         $data = json_decode(curl_exec($ch));
    //         curl_close($ch);

    //         if (isset($data->data)) {
    //             foreach ($data->data as $key => $value) {
    //                 $url = 'https://graph.facebook.com/v15.0/' . $value->id . '?access_token=' . $socialConnect->social_id;
    //                 $ch = curl_init($url);
    //                 curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //                 $data_story = json_decode(curl_exec($ch));
    //                 curl_close($ch);

    //                 if (isset($data_story->story) && $data_story->story != '') {
    //                     $mode = 'livestream';
    //                 } else {
    //                     $mode = '';
    //                 }

    //                 $url = 'https://graph.facebook.com/' . $value->id . '/insights?metric=page_posts_impressions,post_impressions,post_reactions_like_total&access_token=' . $socialConnect->social_id;
    //                 $ch = curl_init($url);
    //                 curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //                 $data_insights = json_decode(curl_exec($ch));
    //                 curl_close($ch);

    //                 $view = @$data_insights->data[0]->values[0]->value ? @$data_insights->data[0]->values[0]->value : 0;
    //                 $like = @$data_insights->data[1]->values[0]->value ? @$data_insights->data[1]->values[0]->value : 0;

    //                 $link = @$value->attachments->data[0]->media->source
    //                     ? @$value->attachments->data[0]->media->source
    //                     : @$value->attachments->data[0]->media->image->src;

    //                 if ($link != '') {
    //                     $fileContents = file_get_contents($link);
    //                     $filename = $value->id . '_facebook';
    //                     Storage::disk('public')->put('social_pics/' . $filename . (@$value->attachments->data[0]->media_type == 'photo' ? '.jpg' : '.mp4'),
    //                         $fileContents
    //                     );
    //                     $file = 'social_pics/' . $filename . (@$value->attachments->data[0]->media_type == 'photo' ? '.jpg' : '.mp4');
    //                 } else {
    //                     $file = '';
    //                 }

    //                 $type = (isset($value->attachments->data[0]->media_type)) ? $value->attachments->data[0]->media_type : "";

    //                 if (@$value->attachments->data[0]->type == 'question') {
    //                     $mode = 'polls';
    //                     $file = @$value->attachments->data[0]->target->url ? @$value->attachments->data[0]->target->url : '';
    //                     $type = '';
    //                 }

    //                 if (
    //                     date('Y-m-d', strtotime($value->created_time)) >= date('Y-m-d', strtotime($influencerRequestDetail->created_at)) &&
    //                     date('Y-m-d', strtotime($value->created_time)) <= date('Y-m-d')
    //                 ) {
    //                     $social = SocialPost::where('user_id', $socialConnect->user_id)
    //                         ->where('media', 'facebook')
    //                         ->where('post_id', $value->id)
    //                         ->first();

    //                     if (isset($social)) {
    //                         $social->update([
    //                             'influencer_request_accept_id' => $mode,
    //                             'text' => (isset($value->message)) ? $value->message : "",
    //                             'link' => (isset($file)) ? $file : "",
    //                             'thumbnail' => 'https://www.facebook.com/' . $value->id,
    //                             'type' => $type,
    //                             'published_at' => date('Y-m-d H:i:s', strtotime($value->created_time)),
    //                             'view' => $view,
    //                             'like' => $like,
    //                         ]);
    //                     } else {
    //                         SocialPost::create([
    //                             'influencer_request_accept_id' => $mode,
    //                             'user_id' => $socialConnect->user_id,
    //                             'media' => 'facebook',
    //                             'post_id' => $value->id,
    //                             'text' => (isset($value->message)) ? $value->message : "",
    //                             'link' => (isset($file)) ? $file : "",
    //                             'thumbnail' => 'https://www.facebook.com/' . $value->id,
    //                             'type' => $type,
    //                             'published_at' => date('Y-m-d H:i:s', strtotime($value->created_time)),
    //                             'view' => $view,
    //                             'like' => $like,
    //                         ]);
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }

    // private function processTikTok($influencerRequestDetail, $socialConnect)
    // {
    //     if (
    //         $socialConnect->token != null &&
    //         $socialConnect->media == $influencerRequestDetail->media &&
    //         $socialConnect->media == 'tiktok'
    //     ) {
    //         $result = SocialKeys::first();
    //         $appId = $result->tiktok_app_id;
    //         $secret = $result->tiktok_app_secret;

    //         $videosApi = "https://open.tiktokapis.com/v2/user/info/?fields=open_id,union_id,avatar_url,follower_count";
    //         $ch = curl_init();
    //         curl_setopt_array($ch, [
    //             CURLOPT_HTTPHEADER => [
    //                 "Authorization: Bearer " . $socialConnect->token
    //             ],
    //             CURLOPT_SSL_VERIFYPEER => false,
    //             CURLOPT_RETURNTRANSFER => true,
    //             CURLOPT_URL => $videosApi
    //         ]);
    //         $response = curl_exec($ch);
    //         curl_close($ch);
    //         $data_tiktok = json_decode($response);

    //         $videos = "https://open.tiktokapis.com/v2/video/list/?fields=id,title,video_description,duration,cover_image_url,share_url,embed_link,create_time,like_count,view_count,share_count,comment_count";
    //         $ch = curl_init();
    //         curl_setopt($ch, CURLOPT_POST, 1);
    //         curl_setopt_array($ch, [
    //             CURLOPT_HTTPHEADER => [
    //                 "Authorization: Bearer " . $socialConnect->token
    //             ],
    //             CURLOPT_SSL_VERIFYPEER => false,
    //             CURLOPT_RETURNTRANSFER => true,
    //             CURLOPT_URL => $videos
    //         ]);
    //         $response = curl_exec($ch);
    //         curl_close($ch);
    //         $content = json_decode($response);

    //         if (isset($content->data->videos)) {
    //             foreach ($content->data->videos as $data_row) {
    //                 $social = SocialPost::where('user_id', $socialConnect->user_id)
    //                     ->where('media', 'tiktok')
    //                     ->where('post_id', $data_row->id)
    //                     ->first();

    //                 if (
    //                     date('Y-m-d', $data_row->create_time) >= date('Y-m-d', strtotime($influencerRequestDetail->created_at)) &&
    //                     date('Y-m-d', $data_row->create_time) <= date('Y-m-d')
    //                 ) {
    //                     if (isset($social)) {
    //                         $social->update([
    //                             'text' => $data_row->title,
    //                             'link' => isset($data_row->share_url) ? $data_row->share_url : "",
    //                             'type' => 'video',
    //                             'published_at' => date('Y-m-d H:i:s', $data_row->create_time),
    //                             'like' => $data_row->like_count,
    //                             'view' => $data_row->view_count,
    //                             'share' => $data_row->share_count,
    //                             'comment' => $data_row->comment_count,
    //                         ]);
    //                     } else {
    //                         SocialPost::create([
    //                             'user_id' => $socialConnect->user_id,
    //                             'media' => 'tiktok',
    //                             'post_id' => $data_row->id,
    //                             'text' => $data_row->title,
    //                             'link' => isset($data_row->share_url) ? $data_row->share_url : "",
    //                             'type' => 'video',
    //                             'published_at' => date('Y-m-d H:i:s', $data_row->create_time),
    //                             'like' => $data_row->like_count,
    //                             'view' => $data_row->view_count,
    //                             'share' => $data_row->share_count,
    //                             'comment' => $data_row->comment_count,
    //                         ]);
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }

    // private function processTwitch($influencerRequestDetail, $socialConnect)
    // {
    //     if (
    //         $socialConnect->token != null &&
    //         $socialConnect->token_secret != null &&
    //         $socialConnect->media == $influencerRequestDetail->media &&
    //         $socialConnect->media == 'twitch'
    //     ) {
    //         $socialKeys = SocialKeys::first();
    //         $accessToken = $socialConnect->token;

    //         $videosApi = 'https://api.twitch.tv/helix/videos?user_id=' . $accessToken;
    //         $ch = curl_init();
    //         curl_setopt_array($ch, [
    //             CURLOPT_HTTPHEADER => [
    //                 "Accept: application/vnd.twitchtv.v5+json",
    //                 'Client-ID: ' . $socialKeys->twitch_app_id,
    //                 "Authorization: Bearer " . $socialConnect->token_secret
    //             ],
    //             CURLOPT_SSL_VERIFYPEER => false,
    //             CURLOPT_RETURNTRANSFER => true,
    //             CURLOPT_URL => $videosApi
    //         ]);
    //         $response = curl_exec($ch);
    //         curl_close($ch);
    //         $data_twitch = json_decode($response, true);

    //         if (isset($data_twitch['data'])) {
    //             foreach ($data_twitch['data'] as $data_row) {
    //                 $social = SocialPost::where('user_id', $socialConnect->user_id)
    //                     ->where('media', 'twitch')
    //                     ->where('post_id', $data_row['id'])
    //                     ->first();
    //                 $file = $data_row['url'];
    //                 if (
    //                     date('Y-m-d', strtotime($data_row['published_at'])) >= date('Y-m-d', strtotime($influencerRequestDetail->created_at)) &&
    //                     date('Y-m-d', strtotime($data_row['published_at'])) <= date('Y-m-d')
    //                 ) {
    //                     if (isset($social)) {
    //                         $social->update([
    //                             'influencer_request_accept_id' => 'livestream',
    //                             'text' => $data_row['title'],
    //                             'link' => $file,
    //                             'type' => 'video',
    //                             'published_at' => date('Y-m-d H:i:s', strtotime($data_row['published_at'])),
    //                             'view' => $data_row['view_count'],
    //                         ]);
    //                     } else {
    //                         SocialPost::create([
    //                             'influencer_request_accept_id' => 'livestream',
    //                             'user_id' => $socialConnect->user_id,
    //                             'media' => 'twitch',
    //                             'post_id' => $data_row['id'],
    //                             'text' => $data_row['title'],
    //                             'link' => $file,
    //                             'type' => 'video',
    //                             'published_at' => date('Y-m-d H:i:s', strtotime($data_row['published_at'])),
    //                             'view' => $data_row['view_count'],
    //                         ]);
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }
}
