<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SocialConnect;
use App\Models\SocialPost;
use Illuminate\Support\Facades\DB;

class UpdateSocialPicsUrls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'social:update-urls';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update social_pics URLs in database to use proper storage paths';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting social_pics URL updates...');

        // Update SocialConnect records
        $this->updateSocialConnectRecords();

        // Update SocialPost records
        $this->updateSocialPostRecords();

        $this->info('Social_pics URL updates completed successfully!');

        return 0;
    }

    private function updateSocialConnectRecords()
    {
        $this->info('Updating SocialConnect records...');

        $records = SocialConnect::whereNotNull('picture')
            ->where('picture', '!=', '')
            ->where('picture', 'not like', 'http%')
            ->get();

        $updatedCount = 0;

        foreach ($records as $record) {
            $oldPicture = $record->picture;
            
            // Skip if already in correct format
            if (strpos($oldPicture, 'social_pics/') === 0) {
                continue;
            }

            // Handle various old formats
            $newPicture = $this->normalizePicturePath($oldPicture);
            
            if ($newPicture !== $oldPicture) {
                $record->update(['picture' => $newPicture]);
                $updatedCount++;
                $this->line("Updated SocialConnect ID {$record->id}: {$oldPicture} -> {$newPicture}");
            }
        }

        $this->info("Updated {$updatedCount} SocialConnect records.");
    }

    private function updateSocialPostRecords()
    {
        $this->info('Updating SocialPost records...');

        $records = SocialPost::whereNotNull('link')
            ->where('link', '!=', '')
            ->where('link', 'not like', 'http%')
            ->get();

        $updatedCount = 0;

        foreach ($records as $record) {
            $oldLink = $record->link;
            
            // Skip if already in correct format
            if (strpos($oldLink, 'social_pics/') === 0) {
                continue;
            }

            // Handle various old formats
            $newLink = $this->normalizePicturePath($oldLink);
            
            if ($newLink !== $oldLink) {
                $record->update(['link' => $newLink]);
                $updatedCount++;
                $this->line("Updated SocialPost ID {$record->id}: {$oldLink} -> {$newLink}");
            }
        }

        $this->info("Updated {$updatedCount} SocialPost records.");
    }

    private function normalizePicturePath($path)
    {
        // Remove any leading slashes or storage/app prefixes
        $path = ltrim($path, '/');
        $path = preg_replace('/^storage\/app\//', '', $path);
        $path = preg_replace('/^storage\//', '', $path);
        
        // Ensure it starts with social_pics/ if it contains social_pics
        if (strpos($path, 'social_pics') !== false && strpos($path, 'social_pics/') !== 0) {
            $path = preg_replace('/.*social_pics\//', 'social_pics/', $path);
        }

        return $path;
    }
}
