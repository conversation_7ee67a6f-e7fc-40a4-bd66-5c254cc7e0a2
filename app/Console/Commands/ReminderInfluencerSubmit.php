<?php

namespace App\Console\Commands;

use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestAccept;
use Socialite;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use App\Models\SocialKeys;
use App\Models\CampaignRequestTime;
use App\Models\User;
use App\Notifications\CancelRefund;

use App\Jobs\NewreminderCampaignTimeisRunningOut;

class ReminderInfluencerSubmit extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminder:submit';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reminder Timeout Influencer Submit';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    { 
        $campaignRequestTime = CampaignRequestTime::first();
        $records =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
                        ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
                        ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
                        ->select('influencer_request_details.*', 'influencer_details.id as i_id','influencer_details.user_id as i_user_id','influencer_request_accepts.request','influencer_request_accepts.request_time','influencer_request_accepts.id as influencer_request_accept_id' )
                        ->get();   

        foreach ($records as $row){   
            $time = (isset($row->request_time_accept) && $row->request_time_accept == '1')?$row->request_time+$row->time:$row->time;
 
            $updated_date =  date('Y-m-d H:i:s',strtotime($row->updated_at));
            $campaignDate= date('Y-m-d H:i:s', strtotime($updated_date. ' + '.$time.' days'));  
 
            $date = date('Y-m-d H:i:s'); 
            $seconds = strtotime($campaignDate) - strtotime($date);

            $days    = floor($seconds / 86400);
            \Log::info($row->compaign_id);
            \Log::info('time'.$time);

            \Log::info('days'.$days);
            if($days < $time && $days >=0 && $row->status==2 && $row->read_status!=null && $row->review == null && $row->refund_reason  != 'Cancelled' && $row->social_post_id == null ){ 
                if($days == '3' || $days == '5' ){ 

                    $influencer =  User::where('id',$row->influencerdetails->user_id)->first(); 
                    \Log::info($row->compaign_id.' Reminder Timeout Influencer Submit');  

                    dispatch(new NewreminderCampaignTimeisRunningOut($influencer, $row )); 
                } 
            }  
        }
    }
}
