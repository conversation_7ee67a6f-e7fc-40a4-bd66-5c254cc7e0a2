<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class FixStoragePaths extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:fix-paths';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix storage paths and move social_pics to public disk';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting storage path fix...');

        // Ensure the public storage directory exists
        if (!Storage::disk('public')->exists('social_pics')) {
            Storage::disk('public')->makeDirectory('social_pics');
            $this->info('Created social_pics directory in public storage.');
        }

        // Check if old directory exists
        $oldPath = public_path('storage/app/social_pics');
        $newPath = storage_path('app/public/social_pics');

        if (File::exists($oldPath)) {
            $this->info("Found old social_pics directory at: {$oldPath}");
            
            $files = File::allFiles($oldPath);
            $movedCount = 0;
            $skippedCount = 0;

            foreach ($files as $file) {
                $filename = $file->getFilename();
                $oldFilePath = $file->getPathname();
                $newFilePath = $newPath . '/' . $filename;
                
                if (!File::exists($newFilePath)) {
                    File::copy($oldFilePath, $newFilePath);
                    $movedCount++;
                } else {
                    $skippedCount++;
                }
            }

            $this->info("Moved {$movedCount} files to new location.");
            $this->info("Skipped {$skippedCount} files (already exist in new location).");

            if ($this->confirm('Do you want to remove the old social_pics directory?')) {
                File::deleteDirectory($oldPath);
                $this->info('Old directory removed.');
            }
        } else {
            $this->info('No old social_pics directory found.');
        }

        // Verify the storage link exists
        $linkPath = public_path('storage');
        $targetPath = storage_path('app/public');

        if (!File::exists($linkPath)) {
            $this->error('Storage link does not exist. Please run: php artisan storage:link');
            return 1;
        }

        if (!is_link($linkPath)) {
            $this->error('Storage path exists but is not a symbolic link. Please remove it and run: php artisan storage:link');
            return 1;
        }

        $this->info('Storage link verified.');
        $this->info('Storage path fix completed successfully!');

        return 0;
    }
}
