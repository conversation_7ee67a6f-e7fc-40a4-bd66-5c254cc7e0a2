<?php

namespace App\Console\Commands;

use App\Jobs\NewReviewPhaseEnded;
use App\Models\Complaint;
use Illuminate\Console\Command;
use App\Models\InfluencerRequestDetail;
use App\Models\User;
use App\Notifications\PaymentRefund;
use Carbon\Carbon;
use App\Models\StripeAccount;
use App\Models\TransferInfluencer;
use Illuminate\Support\Facades\Log;

class InfluencerReview extends Command
{
   /**
    * when review phase Ends - MH
    */
    protected $signature = 'review:influencer';
    protected $description = 'Command description';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $details = InfluencerRequestDetail::where('social_post_id','!=',null)
            ->where('review',null)
            ->where('is_paused','!=',1)
            ->leftjoin('influencer_request_accepts', 'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )
            ->select('influencer_request_details.*','influencer_request_accepts.id as influencer_request_accept_id')
            ->get();
        
        $email_sent_to   = [];
        foreach ($details as $row) {
            $updated_date = date('Y-m-d H:i:s',strtotime($row->updated_at));
            $campaignDate = date('Y-m-d H:i:s', strtotime($updated_date. '+7 days'));
            $date = date('Y-m-d H:i:s');

            if($date >= $campaignDate) {
                $isComplained = Complaint::where('influencer_request_accept_id',$row->influencer_request_accept_id);
                if(!($isComplained->exists() && $isComplained->first()->status=="Inprogress"))
                {
                    $row->update([ 'review'=>'1',
                        'completed_at' => now(),
                        'payment_status' => InfluencerRequestDetail::STATUS_PENDING
                    ]);
                    // dispatch review phase ended email
                    if(!in_array($row->user_id, $email_sent_to))
                    {
                        $customer = User::where('id',$row->user_id)->first();
                        dispatch(new NewReviewPhaseEnded($customer, $row));
                        $email_sent_to[] = $row->user_id;
                    }
                }
            }
        }

        return 0;
    }
}
