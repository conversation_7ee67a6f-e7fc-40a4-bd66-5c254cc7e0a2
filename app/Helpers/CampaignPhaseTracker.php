<?php

namespace App\Helpers;

use Carbon\Carbon;
use Illuminate\Support\Facades\Config;

class CampaignPhaseTracker
{
    /**
     * The timestamp when the campaign was created
     *
     * @var Carbon
     */
    protected $campaignCreatedAt;

    /**
     * The ordered list of campaign phases
     *
     * @var array
     */
    protected $phases = ['request', 'payment', 'submit', 'review'];

    /**
     * The phase durations from config
     *
     * @var array
     */
    protected $phaseDurations;

    /**
     * Create a new CampaignPhaseTracker instance
     *
     * @param string|Carbon $campaignCreatedAt
     */
    public function __construct($campaignCreatedAt)
    {
        // Convert to Carbon if string
        if (is_string($campaignCreatedAt)) {
            $this->campaignCreatedAt = Carbon::parse($campaignCreatedAt);
        } else {
            $this->campaignCreatedAt = $campaignCreatedAt;
        }

        // Load phase durations from config
        $this->phaseDurations = Config::get('app.campaign_phases_duration', [
            'request' => 4320, // 3 days default
            'payment' => 4320, // 3 days default
            'submit' => 14400, // 10 days default
            'review' => 10080, // 7 days default
        ]);
    }

    /**
     * Get the current phase based on the current time
     *
     * @return string|null
     */
    public function getCurrentPhase()
    {
        $now = Carbon::now();
        $startTime = $this->campaignCreatedAt->copy();

        foreach ($this->phases as $phase) {
            $phaseEndTime = $startTime->copy()->addMinutes($this->phaseDurations[$phase]);
            
            if ($now < $phaseEndTime) {
                return $phase;
            }
            
            $startTime = $phaseEndTime;
        }

        // If we're past all phases
        return 'completed';
    }

    /**
     * Get the next phase based on the current time
     *
     * @return string|null
     */
    public function getNextPhase()
    {
        $currentPhase = $this->getCurrentPhase();
        
        if ($currentPhase === 'completed') {
            return null;
        }
        
        $currentPhaseIndex = array_search($currentPhase, $this->phases);
        
        if ($currentPhaseIndex !== false && $currentPhaseIndex < count($this->phases) - 1) {
            return $this->phases[$currentPhaseIndex + 1];
        }
        
        return null;
    }

    /**
     * Get the duration of the current phase in minutes
     *
     * @return int
     */
    public function getCurrentPhaseDurationInMinutes()
    {
        $currentPhase = $this->getCurrentPhase();
        
        if ($currentPhase === 'completed') {
            return 0;
        }
        
        return $this->phaseDurations[$currentPhase];
    }

    /**
     * Get the duration of the current phase in days
     *
     * @return float
     */
    public function getCurrentPhaseDurationInDays()
    {
        return $this->getCurrentPhaseDurationInMinutes() / 1440; // 1440 minutes in a day
    }

    /**
     * Get the duration of the next phase in minutes
     *
     * @return int
     */
    public function getNextPhaseDurationInMinutes()
    {
        $nextPhase = $this->getNextPhase();
        
        if ($nextPhase === null) {
            return 0;
        }
        
        return $this->phaseDurations[$nextPhase];
    }

    /**
     * Get the duration of the next phase in days
     *
     * @return float
     */
    public function getNextPhaseDurationInDays()
    {
        return $this->getNextPhaseDurationInMinutes() / 1440; // 1440 minutes in a day
    }

    /**
     * Get the timestamp when the current phase ends
     *
     * @return Carbon|null
     */
    public function currentPhaseEndsAt()
    {
        $currentPhase = $this->getCurrentPhase();
        
        if ($currentPhase === 'completed') {
            return null;
        }
        
        return $this->phaseEndsAt($currentPhase);
    }

    /**
     * Get the timestamp when the next phase ends
     *
     * @return Carbon|null
     */
    public function nextPhaseEndsAt()
    {
        $nextPhase = $this->getNextPhase();
        
        if ($nextPhase === null) {
            return null;
        }
        
        return $this->phaseEndsAt($nextPhase);
    }

    /**
     * Get the timestamp when a specific phase ends
     *
     * @param string $targetPhase
     * @return Carbon|null
     */
    public function phaseEndsAt($targetPhase)
    {
        if (!in_array($targetPhase, $this->phases)) {
            return null;
        }
        
        $startTime = $this->campaignCreatedAt->copy();
        
        foreach ($this->phases as $phase) {
            $phaseEndTime = $startTime->copy()->addMinutes($this->phaseDurations[$phase]);
            
            if ($phase === $targetPhase) {
                return $phaseEndTime;
            }
            
            $startTime = $phaseEndTime;
        }
        
        return null;
    }

    /**
     * Get the timestamp when a specific phase starts
     *
     * @param string $targetPhase
     * @return Carbon|null
     */
    public function phaseStartsAt($targetPhase)
    {
        if (!in_array($targetPhase, $this->phases)) {
            return null;
        }
        
        if ($targetPhase === $this->phases[0]) {
            return $this->campaignCreatedAt->copy();
        }
        
        $targetPhaseIndex = array_search($targetPhase, $this->phases);
        $previousPhase = $this->phases[$targetPhaseIndex - 1];
        
        return $this->phaseEndsAt($previousPhase);
    }

    /**
     * Check if a specific phase is active
     *
     * @param string $phase
     * @return bool
     */
    public function isPhaseActive($phase)
    {
        return $this->getCurrentPhase() === $phase;
    }

    /**
     * Check if a specific phase is completed
     *
     * @param string $phase
     * @return bool
     */
    public function isPhaseCompleted($phase)
    {
        $phaseIndex = array_search($phase, $this->phases);
        $currentPhaseIndex = array_search($this->getCurrentPhase(), $this->phases);
        
        // If current phase is 'completed', all phases are completed
        if ($this->getCurrentPhase() === 'completed') {
            return true;
        }
        
        // If phase not found or current phase is 'completed'
        if ($phaseIndex === false || $currentPhaseIndex === false) {
            return false;
        }
        
        return $phaseIndex < $currentPhaseIndex;
    }

    /**
     * Get the time remaining in the current phase
     *
     * @param string $unit (seconds, minutes, hours, days)
     * @return float
     */
    public function timeRemainingInCurrentPhase($unit = 'seconds')
    {
        $phaseEndTime = $this->currentPhaseEndsAt();
        
        if ($phaseEndTime === null) {
            return 0;
        }
        
        $now = Carbon::now();
        $diffInSeconds = $phaseEndTime->diffInSeconds($now, false);
        
        if ($diffInSeconds <= 0) {
            return 0;
        }
        
        switch ($unit) {
            case 'minutes':
                return $diffInSeconds / 60;
            case 'hours':
                return $diffInSeconds / 3600;
            case 'days':
                return $diffInSeconds / 86400;
            case 'seconds':
            default:
                return $diffInSeconds;
        }
    }

    /**
     * Get the percentage of completion for the current phase
     *
     * @return float
     */
    public function currentPhaseCompletionPercentage()
    {
        $currentPhase = $this->getCurrentPhase();
        
        if ($currentPhase === 'completed') {
            return 100;
        }
        
        $phaseStartTime = $this->phaseStartsAt($currentPhase);
        $phaseEndTime = $this->phaseEndsAt($currentPhase);
        $now = Carbon::now();
        
        $totalDuration = $phaseEndTime->diffInSeconds($phaseStartTime);
        $elapsedDuration = $now->diffInSeconds($phaseStartTime);
        
        $percentage = ($elapsedDuration / $totalDuration) * 100;
        
        return min(100, max(0, $percentage));
    }

    /**
     * Get all phases with their start and end times
     *
     * @return array
     */
    public function getAllPhaseTimelines()
    {
        $result = [];
        $startTime = $this->campaignCreatedAt->copy();
        
        foreach ($this->phases as $phase) {
            $phaseEndTime = $startTime->copy()->addMinutes($this->phaseDurations[$phase]);
            
            $result[$phase] = [
                'starts_at' => $startTime->copy(),
                'ends_at' => $phaseEndTime->copy(),
                'duration_minutes' => $this->phaseDurations[$phase],
                'duration_days' => $this->phaseDurations[$phase] / 1440,
                'is_active' => $this->isPhaseActive($phase),
                'is_completed' => $this->isPhaseCompleted($phase),
            ];
            
            $startTime = $phaseEndTime;
        }
        
        return $result;
    }

    /**
     * Get the total campaign duration in minutes
     *
     * @return int
     */
    public function getTotalCampaignDurationInMinutes()
    {
        return array_sum($this->phaseDurations);
    }

    /**
     * Get the total campaign duration in days
     *
     * @return float
     */
    public function getTotalCampaignDurationInDays()
    {
        return $this->getTotalCampaignDurationInMinutes() / 1440;
    }

    /**
     * Get the expected campaign completion date
     *
     * @return Carbon
     */
    public function getExpectedCompletionDate()
    {
        return $this->campaignCreatedAt->copy()->addMinutes($this->getTotalCampaignDurationInMinutes());
    }
}