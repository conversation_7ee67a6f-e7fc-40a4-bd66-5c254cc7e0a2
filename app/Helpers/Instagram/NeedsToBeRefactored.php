<?php

class NeedsToBeRefactored {
    /**
     * Influencer submission handler for instagram post livestream
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitInstagramLivestream($record, $row) {
        $instagramUserId = $record->token_secret;
        $accessToken = $record->token;
        $type = '';

        $url = 'https://graph.facebook.com/v18.0/' . $instagramUserId . '/live_media?fields=id,' .
            'media_type,media_product_type,media_url,permalink,owner,username,comments&access_token=' . $accessToken;
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $data1 = json_decode(curl_exec($ch));
        curl_close($ch);

        if (isset($data1->data)) {
            foreach ($data1->data as $key => $value) {
                $mode = 'livestream';
                $social = SocialPost::where('user_id', $record->user_id)->where('media', 'instagram')->where('post_id', $value->id)->first();
                $file = '';
                $media = '';

                $likes = 0;
                $shares = 0;
                $views = 0;
                $comments = isset($value->comments) ?
                    count($value->comments->data) :
                    (
                        ($row->advertising == "Story" || $row->advertising == "Story - Video") ?
                            null : 0
                    );

                $url = "https://graph.facebook.com/" . $instagramUserId . "?fields=id,name,username," .
                    "profile_picture_url,followers_count&access_token=" . $accessToken;
                $client = new Client();
                $response = $client->request('GET', $url);
                $content = $response->getBody()->getContents();
                $oAuth = json_decode($content);
                $name = ($oAuth->username) ? $oAuth->username : $oAuth->name;

                $file = "https://www.instagram.com/{$name}/live/";
                $value->timestamp = date('Y-m-d');
                $media = 'video';
                if (isset($social)) {
                    $social->update([
                        'influencer_request_accept_id' => $mode,
                        'text' => $value->caption,
                        'link' => (isset($file)) ? $file : "",
                        'type' => $media,
                        'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
                        'thumbnail' => $value->permalink,
                        'like' => (isset($likes)) ? $likes : 0,
                        'view' => (isset($views)) ? $views : 0,
                        'share' => (isset($shares)) ? $shares : 0,
                        'comment' => (isset($comments)) ? $comments : ((($row->advertising == "Story" || $row->advertising == "Story - Video") ? null : 0)),
                    ]);
                } else {
                    SocialPost::create([
                        'influencer_request_accept_id' => $mode,
                        'user_id' => $record->user_id,
                        'media' =>  'instagram',
                        'post_id' => $value->id,
                        'text' => $value->caption,
                        'link' => (isset($file)) ? $file : "",
                        'type' => $media,
                        'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
                        'thumbnail' => $value->permalink,
                        'like' => (isset($likes)) ? $likes : 0,
                        'view' => (isset($views)) ? $views : 0,
                        'share' => (isset($shares)) ? $shares : 0,
                        'comment' => (isset($comments)) ? $comments : ((($row->advertising == "Story" || $row->advertising == "Story - Video") ? null : 0)),
                    ]);
                }
            }
        }
    }
}