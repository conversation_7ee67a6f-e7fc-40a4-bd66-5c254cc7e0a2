<?php

namespace App\Helpers\Instagram;

use App\Models\SocialPost;
use App\Models\SocialConnect;
use App\Models\InfluencerRequestDetail;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class InsightsForTypeStory
{
    protected SocialConnect $socialConnect;
    protected InfluencerRequestDetail $influencerRequestDetail;

    protected array $postFields = ['id', 'caption', 'media_type', 'thumbnail_url', 'media_url', 'permalink', 'timestamp'];
    protected array $postMetrics = ['likes', 'comments', 'shares', 'reach'];

    public function __construct(SocialConnect $socialConnect, InfluencerRequestDetail $influencerRequestDetail)
    {
        $this->socialConnect = $socialConnect;
        $this->influencerRequestDetail = $influencerRequestDetail;
    }

    public function importSocialMediaStory() {
        $instagramUserId = $this->socialConnect->token_secret;
        $accessToken = $this->socialConnect->token;
        $type = '';

        $url1 = 'https://graph.facebook.com/v18.0/' . $instagramUserId . '/stories?fields=id,caption,' .
            'media_type,thumbnail_url,media_url,permalink,timestamp&access_token=' . $accessToken;
        $ch1 = curl_init($url1);
        curl_setopt($ch1, CURLOPT_RETURNTRANSFER, true);
        $data1 = json_decode(curl_exec($ch1));
        curl_close($ch1);
        if (isset($data1->data)) {
            foreach ($data1->data as $key => $value) {
                $social = SocialPost::where('user_id', $this->socialConnect->user_id)->where('media', 'instagram')->where('post_id', $value->id)->first();
                $url = 'https://graph.facebook.com/v18.0/' .
                    $value->id . '/insights?metric=' .
                    'views,reach,replies,shares,total_interactions&access_token=' . $accessToken;
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $data = json_decode(curl_exec($ch));
                curl_close($ch);
                $file = '';
                $media = '';

                $likes = 0;
                $shares = 0;
                $comments = 0;
                $views = 0;

                if (isset($data->data)) {
                    foreach ($data->data as $data_count) {
                        if (isset($data_count->name) && $data_count->name == 'reach') {
                            $comments = $data_count->values[0]->value;
                        }

                        if (isset($data_count->name) && $data_count->name == 'views') {
                            $views = $data_count->values[0]->value;
                        }
                        
                        if (isset($data_count->name) && $data_count->name == 'total_interactions') {
                            $likes = $data_count->values[0]->value;
                        }

                        if (isset($data_count->name) && $data_count->name == 'shares') {
                            $shares = $data_count->values[0]->value;
                        }
                    }
                }

                if (
                    date('Y-m-d', strtotime($value->timestamp)) >= date('Y-m-d', strtotime($this->influencerRequestDetail->created_at)) &&
                    date('Y-m-d', strtotime($value->timestamp)) <= date('Y-m-d')
                ) {
                    if (isset($value->media_url)) {
                        $fileContents = file_get_contents($value->media_url);
                        $filename = $value->id . '_instagram';
                        Storage::disk('public')->put('social_pics/' . $filename . ($value->media_type == 'IMAGE' || $value->media_type == 'CAROUSEL_ALBUM' ? '.jpg' : '.mp4'), $fileContents);
                        $file = 'social_pics/' . $filename . ($value->media_type == 'IMAGE' || $value->media_type == 'CAROUSEL_ALBUM' ? '.jpg' : '.mp4');
                        if ($value->media_type == 'IMAGE' || $value->media_type == 'CAROUSEL_ALBUM') {
                            $media = 'photo';
                        } else {
                            $media = 'video';
                        }
                    }

                    $permalink = ($value->permalink != '') ? $value->permalink : 'https://www.instagram.com/stories/' . $this->socialConnect->name;
                    if (isset($social)) {
                        $social->update([
                            'influencer_request_accept_id' => 'story',
                            'text' => $value->caption,
                            'link' => (isset($file)) ? $file : "",
                            'type' => $media,
                            'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
                            'thumbnail' => $permalink,
                            'like' => max($likes, $social->like),
                            'view' => max($views, $social->view),
                            'share' => max($shares, $social->share),
                            'comment' => max($comments, $social->comment),
                        ]);
                    } else {
                        SocialPost::create([
                            'influencer_request_accept_id' => 'story',
                            'user_id' => $this->socialConnect->user_id,
                            'media' =>  'instagram',
                            'post_id' => $value->id,
                            'text' => $value->caption,
                            'link' => (isset($file)) ? $file : "",
                            'type' => $media,
                            'published_at' => date('Y-m-d H:i:s', strtotime($value->timestamp)),
                            'thumbnail' => $permalink,
                            'like' => $likes,
                            'view' => $views,
                            'share' => $shares,
                            'comment' => $comments,
                        ]);
                    }
                }
            }
        }
    }
}
