<?php

namespace App\Helpers\Instagram;

use Carbon\Carbon;
use Illuminate\Support\Facades\Config;

class InsightsForTypePost
{
    protected $socialConnect;
    protected $influencerRequestDetail;

    protected $postFields = ['id', 'caption', 'media_type', 'thumbnail_url', 'media_url', 'permalink', 'timestamp'];
    protected $postMetircs = ['likes', 'comments', 'shares', 'reach'];

    public function __construct($socialConnect, $influencerRequestDetail)
    {
        $this->socialConnect = $socialConnect;
        $this->influencerRequestDetail = $influencerRequestDetail;
    }
    
    public function importSocialMediaPost() {
        $baseApiUrl = 'https://graph.facebook.com/v18.0/' . $this->socialConnect->token_secret . '/media?&access_token=' . $socialConnect->token;
        $callApiUrl = $baseApiUrl . '&fields=' . implode(',', $this->postFields);

        $ch = curl_init($callApiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $socialPostData = json_decode(curl_exec($ch));
        curl_close($ch);

        if (isset($socialPostData->data)) {
            foreach ($socialPostData->data as $socialPostDataItem) {
                $postType = '';
                if (str_contains($socialPostDataItem->permalink, '/reel/')) {
                    $postType = 'reel';
                }

                if (empty($socialPostDataItem->id)) {
                    Log::error('Instagram post missing ID during submission processing', [
                        'user_id' => $socialConnect->user_id,
                        'influencer_request_id' => $influencerRequestDetail->id,
                        'post_data' => $socialPostDataItem,
                        'permalink' => $socialPostDataItem->permalink ?? 'N/A',
                        'media_type' => $socialPostDataItem->media_type ?? 'N/A',
                        'timestamp' => $socialPostDataItem->timestamp ?? 'N/A'
                    ]);
                    continue;
                }
                
                $insightUrl = 'https://graph.facebook.com/v18.0/' . $socialPostDataItem->id . '/insights?access_token=' . $socialConnect->token;
                $insightUrl .= '&metric=' . implode(',', $this->postMetircs);

                $ch = curl_init($insightUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $insightData = json_decode(curl_exec($ch));
                curl_close($ch);

                $media = '';

                $preparedMetricsData = [];
                
                if (isset($insightData->data)) {
                    $preparedMetricsData['complete__' . date('Y_m_d_H_i_s')] = $insightDataItem;
                    foreach ($insightData->data as $insightDataItem) {
                        $preparedMetricsData[$insightDataItem->name] = $insightDataItem->values[0]->value;
                    }
                }

                if (
                    date('Y-m-d', strtotime($socialPostDataItem->timestamp)) >= date('Y-m-d', strtotime($influencerRequestDetail->created_at)) &&
                    date('Y-m-d', strtotime($socialPostDataItem->timestamp)) <= date('Y-m-d')
                ) {
                    if (isset($socialPostDataItem->media_url)) {
                        $fileContents = file_get_contents($socialPostDataItem->media_url);
                        $filename = $socialPostDataItem->id . '_instagram';
                        
                        $fileExt = '.mp4';
                        $contentType = 'video';
                        if (
                            $socialPostDataItem->media_type == 'IMAGE' ||
                            $socialPostDataItem->media_type == 'CAROUSEL_ALBUM'
                        ) {
                            $fileExt = '.jpg';
                            $contentType = 'photo';
                        }

                        $filenameWithExt = $filename . $fileExt;
                        $filepath = 'social_pics/' . $filenameWithExt;
                        
                        Storage::disk('public')->put($filepath, $fileContents);
                    }

                    $socialPost = SocialPost::where('user_id', $socialConnect->user_id)->where('media', 'instagram')->where('post_id', $socialPostDataItem->id)->first();
                    if ($socialPost) {
                        $socialPost->update([
                            'influencer_request_accept_id' => $postType,
                            'text' => $socialPostDataItem->caption,
                            'link' => $filepath,
                            'type' => $contentType,
                            'published_at' => date('Y-m-d H:i:s', strtotime($socialPostDataItem->timestamp)),
                            'thumbnail' => $socialPostDataItem->permalink,
                            'insights' => $preparedMetricsData
                        ]);
                    } else {
                        SocialPost::create([
                            'influencer_request_accept_id' => $postType,
                            'user_id' => $socialConnect->user_id,
                            'media' =>  'instagram',
                            'post_id' => $socialPostDataItem->id,
                            'text' => $socialPostDataItem->caption,
                            'link' => $filepath,
                            'type' => $contentType,
                            'published_at' => date('Y-m-d H:i:s', strtotime($socialPostDataItem->timestamp)),
                            'thumbnail' => $socialPostDataItem->permalink,
                            'insights' => $preparedMetricsData
                        ]);
                    }
                }
            }
        }
    }
}