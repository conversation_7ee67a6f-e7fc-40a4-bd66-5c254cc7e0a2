<?php

class NeedsToBeRefactored {
    /**
     * Influencer submission handler for youtube post
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitYoutube($record, $row) {
        $type = '';
        $result = SocialKeys::first();
        try {
            $youtube_subscribers = @file_get_contents(
                'https://www.googleapis.com/youtube/v3/channels?part=statistics&id=' .
                $record->social_id .
                '&key=' . env('YOUTUBE_API_KEY')
            );
            $youtube_api_response = json_decode($youtube_subscribers, true);
            $youtube_subscribers1 = @file_get_contents(
                'https://www.googleapis.com/youtube/v3/search?order=date&part=snippet&channelId=' .
                $youtube_api_response['items'][0]['id'] .
                '&key=' . env('YOUTUBE_API_KEY')
            );

            $data_youtube = json_decode($youtube_subscribers1, true);

            $youtube_subscribers3 = file_get_contents(
                'https://yt.lemnoslife.com/channels?part=shorts&id=' .
                $youtube_api_response['items'][0]['id'] .
                '&key=' . env('YOUTUBE_API_KEY')
            );

            $data_youtube3 = json_decode($youtube_subscribers3, true);

            $mode = '';
            $count = 1;
            if ($data_youtube != '') {
                foreach ($data_youtube['items'] as $data_row) {
                    if ($count < count($data_youtube['items']) &&  isset($data_row['id']['videoId'])) {
                        $mode = '';
                        foreach ($data_youtube3['items'][0]['shorts'] as $data_row3) {
                            if (isset($data_row3['videoId'])) {
                                if ($data_row['id']['videoId'] == $data_row3['videoId']) {
                                    $mode = 'shorts';
                                }
                            }
                        }

                        $youtube_subscribers4 = file_get_contents(
                            'https://www.googleapis.com/youtube/v3/videos?part=liveStreamingDetails&id=' .
                            $data_row['id']['videoId'] .
                            '&key=' . env('YOUTUBE_API_KEY')
                        );

                        $data_youtube4 = json_decode($youtube_subscribers4, true);

                        if (isset($data_youtube4['items'][0]['liveStreamingDetails'])) {
                            $mode = 'livestream';
                        }

                        $youtube_subscribers2 = file_get_contents(
                            'https://www.googleapis.com/youtube/v3/videos?part=statistics&id=' .
                            $data_row['id']['videoId'] .
                            '&key=' . env('YOUTUBE_API_KEY')
                        );

                        $data_youtube2 = json_decode($youtube_subscribers2, true);

                        $social = SocialPost::where('user_id', $record->user_id)->where('media', 'youtube')->where('post_id', $data_row["etag"])->first();
                        $file = '';

                        if (
                            date('Y-m-d', strtotime($data_row["snippet"]["publishedAt"])) >= date('Y-m-d', strtotime($row->created_at)) &&
                            date('Y-m-d', strtotime($data_row["snippet"]["publishedAt"])) <= date('Y-m-d')
                        ) {
                            if (isset($social)) {
                                $social->update([
                                    'influencer_request_accept_id' => $mode,
                                    'text' => $data_row['snippet']['title'],
                                    'link' => 'https://www.youtube.com/watch?v=' . $data_row['id']['videoId'],
                                    'type' => 'video',
                                    'published_at' => date('Y-m-d H:i:s', strtotime($data_row["snippet"]["publishedAt"])),
                                    'like' => (
                                        isset(
                                            $data_youtube2['items'][0]['statistics']['likeCount']) &&
                                            $data_youtube2['items'][0]['statistics']['likeCount'] > 0
                                        ) ? $data_youtube2['items'][0]['statistics']['likeCount'] : 0,
                                    'view' => (
                                        isset(
                                            $data_youtube2['items'][0]['statistics']['viewCount']) &&
                                            $data_youtube2['items'][0]['statistics']['viewCount'] > 0
                                        ) ? $data_youtube2['items'][0]['statistics']['viewCount'] : 0,
                                    'comment' => (
                                        isset(
                                            $data_youtube2['items'][0]['statistics']['commentCount']) &&
                                            $data_youtube2['items'][0]['statistics']['commentCount'] > 0
                                        ) ? $data_youtube2['items'][0]['statistics']['commentCount'] : 0,
                                ]);
                            } else {
                                SocialPost::create([
                                    'influencer_request_accept_id' => $mode,
                                    'user_id' => $record->user_id,
                                    'media' =>  'youtube',
                                    'post_id' => $data_row["etag"],
                                    'text' => $data_row['snippet']['title'],
                                    'link' => 'https://www.youtube.com/watch?v=' . $data_row['id']['videoId'],
                                    'type' => 'video',
                                    'published_at' => date('Y-m-d H:i:s', strtotime($data_row["snippet"]["publishedAt"])),
                                    'like' => (
                                        isset(
                                            $data_youtube2['items'][0]['statistics']['likeCount']) &&
                                            $data_youtube2['items'][0]['statistics']['likeCount'] > 0
                                        ) ? $data_youtube2['items'][0]['statistics']['likeCount'] : 0,
                                    'view' => (
                                        isset(
                                            $data_youtube2['items'][0]['statistics']['viewCount']) &&
                                            $data_youtube2['items'][0]['statistics']['viewCount'] > 0
                                        ) ? $data_youtube2['items'][0]['statistics']['viewCount'] : 0,
                                    'comment' => (
                                        isset(
                                            $data_youtube2['items'][0]['statistics']['commentCount']) &&
                                            $data_youtube2['items'][0]['statistics']['commentCount'] > 0
                                        ) ? $data_youtube2['items'][0]['statistics']['commentCount'] : 0,
                                ]);
                            }
                        }
                    }

                    //community polls
                    $youtube_subscribers5 = file_get_contents(
                        'https://yt.lemnoslife.com/channels?part=community&id=' . $youtube_api_response['items'][0]['id']
                    );
                    $data_youtube5 = json_decode($youtube_subscribers5, true);

                    foreach ($data_youtube5['items'] as $data_row) {
                        if (isset($data_row['id'])) {
                            $mode = 'polls';

                            $social = SocialPost::where('user_id', $record->user_id)->where('media', 'youtube')->where('post_id', $data_row["id"])->first();
                            $file = '';

                            if (isset($social)) {
                                $social->update([
                                    'influencer_request_accept_id' => $mode,
                                    'text' => $data_row['community'][0]['contentText'][0]['text'],
                                    'link' => 'https://www.youtube.com/' . $record->token_secret . '/community',
                                    'type' => '',
                                    'published_at' => date('Y-m-d H:i:s'),
                                    'like' => (isset($data_row['community'][0]['likes']) && $data_row['community'][0]['likes'] > 0) ? $data_row['community'][0]['likes'] : 0,
                                    'comment' => (isset($data_row['community'][0]['commentsCount']) && $data_row['community'][0]['commentsCount'] > 0) ? $data_row['community'][0]['commentsCount'] : 0,
                                ]);
                            } else {
                                $query = SocialPost::create([
                                    'influencer_request_accept_id' => $mode,
                                    'user_id' => $record->user_id,
                                    'media' =>  'youtube',
                                    'post_id' => $data_row['id'],
                                    'text' => $data_row['community'][0]['contentText'][0]['text'],
                                    'link' => 'https://www.youtube.com/' . $record->tokenSecret . '/community',
                                    'type' => '',
                                    'published_at' => date('Y-m-d H:i:s'),
                                    'like' => (isset($data_row['community'][0]['likes']) && $data_row['community'][0]['likes'] > 0) ? $data_row['community'][0]['likes'] : 0,
                                    'comment' => (isset($data_row['community'][0]['commentsCount']) && $data_row['community'][0]['commentsCount'] > 0) ? $data_row['community'][0]['commentsCount'] : 0,
                                ]);
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // TODO how to handle this error?
            // return back()->with('error', 'Something went wrong!');
        }
    }
}