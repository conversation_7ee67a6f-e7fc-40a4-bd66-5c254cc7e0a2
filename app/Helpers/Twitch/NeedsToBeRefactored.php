<?php

class NeedsToBeRefactored {
    /**
     * Influencer submission handler for twitch post
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitTwitch($record, $row) {
        $socialKeys = SocialKeys::first();
        $accessToken = $record->token;
        $type = '';

        $videosApi = 'https://api.twitch.tv/helix/videos?user_id=' . $accessToken;
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_HTTPHEADER => [
                "Accept: application/vnd.twitchtv.v5+json",
                'Client-ID: ' . $socialKeys->twitch_app_id,
                "Authorization: Bearer " . $record->token_secret
            ],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_URL => $videosApi
        ]);
        $response = curl_exec($ch);
        curl_close($ch);
        $data_twitch = json_decode($response, JSON_PRETTY_PRINT);

        if (isset($data_twitch['data'])) {
            foreach ($data_twitch['data'] as $data_row) {
                $social = SocialPost::where('user_id', $record->user_id)->where('media', 'twitch')->where('post_id', $data_row['id'])->first();
                $file =  $data_row['url'];
                if (
                    date('Y-m-d', strtotime($data_row['published_at'])) >= date('Y-m-d', strtotime($row->created_at)) &&
                    date('Y-m-d', strtotime($data_row['published_at'])) <= date('Y-m-d')
                ) {
                    if (isset($social)) {
                        $social->update([
                            'influencer_request_accept_id' => 'livestream',
                            'text' => $data_row['title'],
                            'link' => $file,
                            'type' => 'video',
                            'published_at' => date('Y-m-d H:i:s', strtotime($data_row['published_at'])),
                            'view' => $data_row['view_count'],
                        ]);
                    } else {
                        SocialPost::create([
                            'influencer_request_accept_id' => 'livestream',
                            'user_id' => $record->user_id,
                            'media' =>  'twitch',
                            'post_id' => $data_row['id'],
                            'text' => $data_row['title'],
                            'link' => $file,
                            'type' =>  'video',
                            'published_at' => date('Y-m-d H:i:s', strtotime($data_row['published_at'])),
                            'view' => $data_row['view_count'],
                        ]);
                    }
                }
            }
        }
    }
}