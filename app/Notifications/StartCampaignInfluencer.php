<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;


class StartCampaignInfluencer extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public $user;

    public function __construct($customer,$influencerDetail)
    { 
        $this->customer = $customer;
        $this->influencerDetail = $influencerDetail;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        // return (new MailMessage)
        //     ->greeting('Hello ' . ucfirst($this->customer->first_name) . ' ' . ucfirst($this->customer->last_name).',') 
        //     ->subject(env('APP_NAME').' -  Campaign started')
        //     ->line('This is to inform that customer has started your campaign request.The details are as below:')  
        //     ->line('<strong>Media:</strong> '.(isset($this->influencerDetail->media)?$this->influencerDetail->media:'Not mentioned'))
        //     ->line('<strong>Advertising:</strong> '.(isset($this->influencerDetail->advertising)?$this->influencerDetail->advertising:'Not mentioned'))
        //     ->line('<strong>Name:</strong> '.(isset($this->influencerDetail->name)?$this->influencerDetail->name:'Not mentioned'))
        //     ->line('<strong>Hashtags:</strong> '.((isset($this->influencerDetail->hashtags) && strlen($this->influencerDetail->hashtags)>1)?$this->influencerDetail->hashtags:'Not mentioned'))
        //     ->line('<strong>Time:</strong> '.(isset($this->influencerDetail->time)?$this->influencerDetail->time.' days':'Not mentioned'))
        //     ->line('<strong>Store Link:</strong> '.(isset($this->influencerDetail->link)?$this->influencerDetail->link:'Not mentioned'))
        //     ->line('Thank you for using our website!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            "message"=>"Campaign started",
            "link" => url('/active-campaigns'),
        ];
    }
}
