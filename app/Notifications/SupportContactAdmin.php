<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;


class SupportContactAdmin extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public $user;

    public function __construct($influencer,$user,$support)
    {
        $this->user = $user;
        $this->influencer = $influencer;
        $this->support = $support;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        // $mail = (new MailMessage)
        //     ->greeting('Hello, Admin' )
        //     ->subject(env('APP_NAME').' - Influencer Contacted')
        //     ->line('This is to inform that influencer ' . ucfirst($this->influencer->first_name) . '  ' . ucfirst($this->influencer->last_name) .' has contacted regarding their content is not show up on submission for Campaign. The details are as below:')  
        //     ->line('<strong>Customer Name:</strong> '.ucfirst($this->user->first_name) . '  ' . ucfirst($this->user->last_name)) 
        //     ->line('<strong>Campaign Id:</strong> '.@$this->support->influencerRequestDetail->compaign_id) 
        //     ->line('<strong>Influnecer Comment:</strong> '.$this->support->comment);

        //     if($this->support->file){
        //         $mail->attach(public_path('storage/app/'.$this->support->file));
        //     }
        //     $mail->line('Thank you for using our website!');

        //     return $mail;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            "message"=>"Influencer Contacted",
            'url' => ''
        ];
    }
}