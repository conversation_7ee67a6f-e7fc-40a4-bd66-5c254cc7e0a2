<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;


class SendActivateUser extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */

    public $user;
    public $token;

    public function __construct($user,$token)
    {
        $this->user = $user;
        $this->token = $token;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        return (new MailMessage)
            ->line('<div class="page-mail-title">Activate User</div>')
            ->greeting('Dear ' . ucfirst($this->user->first_name) . ' ' . ucfirst($this->user->last_name))
            ->subject(env('APP_NAME').' - Activate User')
            ->line('It seems like you have forgot your password for '.env('APP_NAME').'. Click the link below and you\'ll be redirected to a secure site from which you can set a new password.')
            ->action('Activate User', url('/password/activate/'.$this->token))
            ->line('If you did not forgot your password you can safely ignore this email.')
            ->line('Thank you for using our website!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
