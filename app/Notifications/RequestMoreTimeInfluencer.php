<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;


class RequestMoreTimeInfluencer extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public $user;

    public function __construct($customer,$request,$influencerDetail)
    { 
        $this->customer = $customer;
        $this->request = $request;
        $this->influencerDetail = $influencerDetail;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail','database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        return (new MailMessage)
            ->greeting('Hello ' . ucfirst($this->customer->first_name) . ' ' . ucfirst($this->customer->last_name).',') 
            ->subject(env('APP_NAME').' - Influencer Request More time ')
            ->line('This is to inform that user ' . ucfirst($this->request->user->first_name) . '  ' . ucfirst($this->request->user->last_name) .' has required more time for  your request.The details are as below:')
            ->line('<strong>Time required:</strong> '.$this->request->request_time.' day(s)')
            ->line('<strong>Media:</strong> '.(isset($this->influencerDetail->media)?$this->influencerDetail->media:'Not mentioned'))
            ->line('<strong>Advertising:</strong> '.(isset($this->influencerDetail->advertising)?$this->influencerDetail->advertising:'Not mentioned'))
            ->line('<strong>Name:</strong> '.(isset($this->influencerDetail->name)?$this->influencerDetail->name:'Not mentioned'))
            ->line('<strong>Hashtags:</strong> '.((isset($this->influencerDetail->hashtags) && strlen($this->influencerDetail->hashtags)>1)?$this->influencerDetail->hashtags:'Not mentioned'))
            ->line('<strong>Time:</strong> '.(isset($this->influencerDetail->time)?$this->influencerDetail->time.' days':'Not mentioned'))
            ->line('<strong>Store Link:</strong> '.(isset($this->influencerDetail->link)?$this->influencerDetail->link:'Not mentioned'))
            ->action('View Request',url('/active-campaigns#'.$this->influencerDetail->compaign_id))
            ->line('Thank you for using our website!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            "message"=> ucfirst($this->request->user->first_name) ." ". ucfirst($this->request->user->last_name) ." has requested More time for request",
            "link" => url('/active-campaigns#'.$this->influencerDetail->compaign_id),
        ];
    }
}
