<?php 
namespace App\Notifications;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class paidToInfluencer extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public $user;
    public $detail;

    public function __construct($user,$detail)
    {
        $this->user = $user;
        $this->detail = $detail;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {        
        // $mail = (new MailMessage)
        //     ->greeting('Hello ' . ucfirst($this->user->first_name) . ' ' . ucfirst($this->user->last_name).',')
        //     ->subject(env('APP_NAME').' - Payment received')
        //     ->line("You have received payment ".$this->detail." for Campaign . Payment was sent to your connected Stripe account.")
        //     ->line('Please login to <a href="'.url('/login').'">'.url('/login').'</a> to access your account.');

        // $mail->line('Thank you for using our platform!');

        // return $mail;

    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    { 
         return [
            'title' => 'Payment received',
            "type" => 'front-tutor',
            "link" => url('/'),
            'msg' => "You have received payment '".$this->detail."' for Campaign "
        ];

    }
}
