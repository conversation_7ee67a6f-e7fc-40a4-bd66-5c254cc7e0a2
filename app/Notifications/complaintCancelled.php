<?php 
namespace App\Notifications;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class complaintCancelled extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public $user;
    public $detail;

    public function __construct($user,$detail)
    {
        $this->user = $user;
        $this->detail = $detail;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {        
        // $mail = (new MailMessage)
        //     ->greeting('Hello ' . ucfirst($this->user->first_name) . ' ' . ucfirst($this->user->last_name).',')
        //     ->subject(env('APP_NAME').' - Payment refunded due to compaign cancelled')
        //     ->line("You have received payment  for Campaign ".$this->detail->compaign_id.". Payment was sent to your connected Stripe account.") ;

        // $mail->line('Thank you for using our platform!');

        // return $mail;

    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    { 
         
        return [
            "message"=> 'Payment refunded due to campaign cancelled for '.$this->detail->compaign_id,
            "link" => url('/active-campaigns'),
        ];

    }
}
