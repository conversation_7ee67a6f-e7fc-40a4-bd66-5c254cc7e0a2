<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;


class RatingSubmit extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public $user;

    public function __construct($influencer,$user,$influencer_request)
    {
        $this->user = $user;
        $this->influencer = $influencer;
        $this->influencer_request = $influencer_request;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        // return (new MailMessage)
        //     ->greeting('Hello '.ucfirst($this->influencer->first_name) . ' ' . ucfirst($this->influencer->last_name).','  )
        //     ->subject(env('APP_NAME').' - Rating submitted')
        //     ->line('This is to inform that user ' . ucfirst($this->user->first_name) . '  ' . ucfirst($this->user->last_name) .' has  submitted rating for you .' ) 
        //     ->line('Thank you for using our website!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            "message"=>"Rating submitted by ". ucfirst($this->user->first_name) . " " . ucfirst($this->user->last_name) , 
            "link" => url('/campaign-history'),
        ];
    }
}