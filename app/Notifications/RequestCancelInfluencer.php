<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;


class RequestCancelInfluencer extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public $user;

    public function __construct($influencer,$user,$formData)
    {
        $this->user = $user;
        $this->influencer = $influencer;
        $this->formData = $formData;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        // return (new MailMessage) 
        //     ->greeting('Hello ' . ucfirst($this->influencer->first_name) . ' ' . ucfirst($this->influencer->last_name) .',')
        //     ->subject(env('APP_NAME').' - Influencer Request Cancel')
        //     ->line('This is to inform that user ' . ucfirst($this->user->first_name) . '  ' . ucfirst($this->user->last_name) .' has cancelled the request. The details are as below:') 
        //     ->line('<strong>Name:</strong> '.(isset($this->formData['name'])?$this->formData['name']:'Not mentioned')) 
        //     ->line('<strong>Hashtags:</strong> '.((isset($this->formData['hashtags']) && strlen($this->formData['hashtags'])>1)?$this->formData['hashtags']:'Not mentioned'))
        //     ->line('<strong>Time:</strong> '.(isset($this->formData['time'])?$this->formData['time'].' days':'Not mentioned'))
        //     ->line('<strong>Store Link:</strong> '.(isset($this->formData['link'])?$this->formData['link']:'Not mentioned') )
        //     ->action('View Request',url('/active-campaigns'))
        //     ->line('Thank you for using our website!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            "message"=> ucfirst($this->user->first_name) . '  ' . ucfirst($this->user->last_name) .' has cancelled the request',
            "link" => url('/campaign-history'),
        ];
    }
}
