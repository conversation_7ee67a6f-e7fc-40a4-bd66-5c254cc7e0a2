<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;


class RegistrationUser extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public $user;
    public function __construct($user)
    {
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        if($this->user->lang == 'En') {
            return (new MailMessage)
                // ->greeting('Hello ' . ucfirst($this->user->user_type) . ', ' )
                ->subject(env('APP_NAME').' - Account Created')
                ->line('Hello '.$this->user->first_name.' '.$this->user->last_name.',')
                ->line('<div class="page-mail-title" >Registration successfully</div>')
                ->line('thank you for your interest in our closed beta!')
                ->line('Please confirm your subscription:')
                ->line('<a target="_blank" rel="noopener noreferrer" href="' . config('app.url') . 'subscribe/en/' . $this->user->email . '" class="button button-primary">Confirm</a>')
                ->line('We would like to inform you that we are currently reviewing your application. You will hear from us shortly. Please note that our closed beta is an exclusive testing period for selected influencers and customers.')
                ->line('Thank you for your interest and patience, and we look forward to getting in touch with you soon.')
                ->line('Thank you for using our website!<br /><br />');
        } else {
            return (new MailMessage)
                // ->greeting('Hello ' . ucfirst($this->user->user_type) . ', ' )
                ->subject(env('APP_NAME').' - Account Created')
                ->line('Hello '.$this->user->first_name.' '.$this->user->last_name.',')
                ->line('<div class="page-mail-title" >Registration successfully</div>')
                ->line('thank you for your interest in our closed beta!')
                ->line('Please confirm your subscription:')
                ->line('<a target="_blank" rel="noopener noreferrer" href="' . config('app.url') . 'subscribe/en/' . $this->user->email . '" class="button button-primary">Confirm</a>')
                ->line('We would like to inform you that we are currently reviewing your application. You will hear from us shortly. Please note that our closed beta is an exclusive testing period for selected influencers and customers.')
                ->line('Thank you for your interest and patience, and we look forward to getting in touch with you soon.')
                ->line('Thank you for using our website!<br /><br />');
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
